﻿// Data Management Module - إدارة البيانات المحسنة
const DataModule = {
    dbName: 'VelaSweetsDB',
    dbVersion: 1,
    db: null,
    
    init: async function() {
        try {
            await this.initIndexedDB();
            await this.migrateFromLocalStorage();
            console.log('Data Module initialized successfully');
        } catch (error) {
            console.error('Data Module initialization failed:', error);
            // Fallback to localStorage
            this.useLocalStorageFallback = true;
        }
    },
    
    // Initialize IndexedDB
    initIndexedDB: function() {
        return new Promise((resolve, reject) => {
            const request = indexedDB.open(this.dbName, this.dbVersion);
            
            request.onerror = () => reject(request.error);
            request.onsuccess = () => {
                this.db = request.result;
                resolve(this.db);
            };
            
            request.onupgradeneeded = (event) => {
                const db = event.target.result;
                
                // Create object stores
                if (!db.objectStoreNames.contains('products')) {
                    const productStore = db.createObjectStore('products', { keyPath: 'id' });
                    productStore.createIndex('category', 'category', { unique: false });
                    productStore.createIndex('name', 'name', { unique: false });
                }
                
                if (!db.objectStoreNames.contains('cart')) {
                    db.createObjectStore('cart', { keyPath: 'id' });
                }
                
                if (!db.objectStoreNames.contains('favorites')) {
                    db.createObjectStore('favorites', { keyPath: 'id' });
                }
                
                if (!db.objectStoreNames.contains('orders')) {
                    const orderStore = db.createObjectStore('orders', { keyPath: 'id' });
                    orderStore.createIndex('status', 'status', { unique: false });
                    orderStore.createIndex('date', 'date', { unique: false });
                }
                
                if (!db.objectStoreNames.contains('profiles')) {
                    db.createObjectStore('profiles', { keyPath: 'id' });
                }
                
                if (!db.objectStoreNames.contains('notifications')) {
                    const notificationStore = db.createObjectStore('notifications', { keyPath: 'id' });
                    notificationStore.createIndex('read', 'read', { unique: false });
                    notificationStore.createIndex('date', 'date', { unique: false });
                }
                
                if (!db.objectStoreNames.contains('sync_data')) {
                    db.createObjectStore('sync_data', { keyPath: 'key' });
                }
                
                if (!db.objectStoreNames.contains('cache')) {
                    const cacheStore = db.createObjectStore('cache', { keyPath: 'key' });
                    cacheStore.createIndex('expiry', 'expiry', { unique: false });
                }
            };
        });
    },
    
    // Migrate data from localStorage to IndexedDB
    migrateFromLocalStorage: async function() {
        const migrations = [
            { key: 'velasweets_cart', store: 'cart' },
            { key: 'velasweets_favorites', store: 'favorites' },
            { key: 'velasweets_orders', store: 'orders' },
            { key: 'velasweets_notifications', store: 'notifications' }
        ];
        
        for (const migration of migrations) {
            try {
                const data = localStorage.getItem(migration.key);
                if (data) {
                    const parsedData = JSON.parse(data);
                    if (Array.isArray(parsedData)) {
                        for (const item of parsedData) {
                            await this.save(migration.store, item);
                        }
                    }
                    // Remove from localStorage after successful migration
                    localStorage.removeItem(migration.key);
                }
            } catch (error) {
                console.warn(Migration failed for :, error);
            }
        }
    },
    
    // Save data to IndexedDB
    save: function(storeName, data) {
        if (this.useLocalStorageFallback) {
            return this.saveToLocalStorage(storeName, data);
        }
        
        return new Promise((resolve, reject) => {
            const transaction = this.db.transaction([storeName], 'readwrite');
            const store = transaction.objectStore(storeName);
            
            // Ensure data has an ID
            if (!data.id) {
                data.id = Date.now() + Math.random();
            }
            
            const request = store.put(data);
            
            request.onsuccess = () => resolve(data);
            request.onerror = () => reject(request.error);
        });
    },
    
    // Get data from IndexedDB
    get: function(storeName, id) {
        if (this.useLocalStorageFallback) {
            return this.getFromLocalStorage(storeName, id);
        }
        
        return new Promise((resolve, reject) => {
            const transaction = this.db.transaction([storeName], 'readonly');
            const store = transaction.objectStore(storeName);
            const request = store.get(id);
            
            request.onsuccess = () => resolve(request.result);
            request.onerror = () => reject(request.error);
        });
    },
    
    // Get all data from a store
    getAll: function(storeName) {
        if (this.useLocalStorageFallback) {
            return this.getAllFromLocalStorage(storeName);
        }
        
        return new Promise((resolve, reject) => {
            const transaction = this.db.transaction([storeName], 'readonly');
            const store = transaction.objectStore(storeName);
            const request = store.getAll();
            
            request.onsuccess = () => resolve(request.result);
            request.onerror = () => reject(request.error);
        });
    },
    
    // Delete data from IndexedDB
    delete: function(storeName, id) {
        if (this.useLocalStorageFallback) {
            return this.deleteFromLocalStorage(storeName, id);
        }
        
        return new Promise((resolve, reject) => {
            const transaction = this.db.transaction([storeName], 'readwrite');
            const store = transaction.objectStore(storeName);
            const request = store.delete(id);
            
            request.onsuccess = () => resolve();
            request.onerror = () => reject(request.error);
        });
    },
    
    // Clear all data from a store
    clear: function(storeName) {
        if (this.useLocalStorageFallback) {
            return this.clearLocalStorage(storeName);
        }
        
        return new Promise((resolve, reject) => {
            const transaction = this.db.transaction([storeName], 'readwrite');
            const store = transaction.objectStore(storeName);
            const request = store.clear();
            
            request.onsuccess = () => resolve();
            request.onerror = () => reject(request.error);
        });
    },
    
    // Search data using index
    searchByIndex: function(storeName, indexName, value) {
        if (this.useLocalStorageFallback) {
            return this.searchLocalStorage(storeName, indexName, value);
        }
        
        return new Promise((resolve, reject) => {
            const transaction = this.db.transaction([storeName], 'readonly');
            const store = transaction.objectStore(storeName);
            const index = store.index(indexName);
            const request = index.getAll(value);
            
            request.onsuccess = () => resolve(request.result);
            request.onerror = () => reject(request.error);
        });
    },
    
    // Cache data with expiry
    cache: async function(key, data, ttl = 3600000) { // Default 1 hour
        const cacheData = {
            key,
            data,
            expiry: Date.now() + ttl,
            created: Date.now()
        };
        
        try {
            await this.save('cache', cacheData);
        } catch (error) {
            console.warn('Cache save failed:', error);
        }
    },
    
    // Get cached data
    getCache: async function(key) {
        try {
            const cached = await this.get('cache', key);
            if (cached && cached.expiry > Date.now()) {
                return cached.data;
            } else if (cached) {
                // Remove expired cache
                await this.delete('cache', key);
            }
            return null;
        } catch (error) {
            console.warn('Cache get failed:', error);
            return null;
        }
    },
    
    // Clean expired cache
    cleanExpiredCache: async function() {
        try {
            const allCache = await this.getAll('cache');
            const now = Date.now();
            
            for (const item of allCache) {
                if (item.expiry < now) {
                    await this.delete('cache', item.key);
                }
            }
        } catch (error) {
            console.warn('Cache cleanup failed:', error);
        }
    },
    
    // Backup data
    backup: async function() {
        const backup = {
            timestamp: Date.now(),
            version: this.dbVersion,
            data: {}
        };
        
        const stores = ['cart', 'favorites', 'orders', 'profiles', 'notifications'];
        
        for (const store of stores) {
            try {
                backup.data[store] = await this.getAll(store);
            } catch (error) {
                console.warn(Backup failed for :, error);
            }
        }
        
        return backup;
    },
    
    // Restore data from backup
    restore: async function(backup) {
        if (!backup || !backup.data) {
            throw new Error('Invalid backup data');
        }
        
        for (const [storeName, items] of Object.entries(backup.data)) {
            try {
                await this.clear(storeName);
                for (const item of items) {
                    await this.save(storeName, item);
                }
            } catch (error) {
                console.warn(Restore failed for :, error);
            }
        }
    },
    
    // Export data as JSON
    export: async function() {
        const backup = await this.backup();
        const blob = new Blob([JSON.stringify(backup, null, 2)], { 
            type: 'application/json' 
        });
        
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = elasweets-backup-.json;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
    },
    
    // Import data from JSON file
    import: function(file) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = async (e) => {
                try {
                    const backup = JSON.parse(e.target.result);
                    await this.restore(backup);
                    resolve();
                } catch (error) {
                    reject(error);
                }
            };
            reader.onerror = () => reject(reader.error);
            reader.readAsText(file);
        });
    },
    
    // LocalStorage fallback methods
    saveToLocalStorage: function(storeName, data) {
        try {
            const key = elasweets_;
            const existing = JSON.parse(localStorage.getItem(key) || '[]');
            const index = existing.findIndex(item => item.id === data.id);
            
            if (index >= 0) {
                existing[index] = data;
            } else {
                existing.push(data);
            }
            
            localStorage.setItem(key, JSON.stringify(existing));
            return Promise.resolve(data);
        } catch (error) {
            return Promise.reject(error);
        }
    },
    
    getFromLocalStorage: function(storeName, id) {
        try {
            const key = elasweets_;
            const data = JSON.parse(localStorage.getItem(key) || '[]');
            const item = data.find(item => item.id === id);
            return Promise.resolve(item);
        } catch (error) {
            return Promise.reject(error);
        }
    },
    
    getAllFromLocalStorage: function(storeName) {
        try {
            const key = elasweets_;
            const data = JSON.parse(localStorage.getItem(key) || '[]');
            return Promise.resolve(data);
        } catch (error) {
            return Promise.reject(error);
        }
    },
    
    deleteFromLocalStorage: function(storeName, id) {
        try {
            const key = elasweets_;
            const data = JSON.parse(localStorage.getItem(key) || '[]');
            const filtered = data.filter(item => item.id !== id);
            localStorage.setItem(key, JSON.stringify(filtered));
            return Promise.resolve();
        } catch (error) {
            return Promise.reject(error);
        }
    },
    
    clearLocalStorage: function(storeName) {
        try {
            const key = elasweets_;
            localStorage.removeItem(key);
            return Promise.resolve();
        } catch (error) {
            return Promise.reject(error);
        }
    }
};

// Register the module
if (typeof VelaSweets !== 'undefined') {
    VelaSweets.registerModule('data', DataModule);
}

// Export for global use
window.DataModule = DataModule;

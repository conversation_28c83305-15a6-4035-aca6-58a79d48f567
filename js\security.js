﻿// Security Module - الأمان والتحقق
const SecurityModule = {
    init: function() {
        this.setupCSP();
        this.initializeRateLimiter();
        this.setupSecurityHeaders();
    },
    
    // Content Security Policy
    setupCSP: function() {
        const meta = document.createElement('meta');
        meta.httpEquiv = 'Content-Security-Policy';
        meta.content = 
            default-src 'self';
            script-src 'self' 'unsafe-inline';
            style-src 'self' 'unsafe-inline' https://fonts.googleapis.com;
            font-src 'self' https://fonts.gstatic.com;
            img-src 'self' data: https:;
            connect-src 'self';
        .replace(/\s+/g, ' ').trim();
        
        document.head.appendChild(meta);
    },
    
    // Input validation and sanitization
    validateInput: function(input, type, options = {}) {
        if (typeof input !== 'string') {
            return input;
        }
        
        // Basic sanitization
        let sanitized = input
            .replace(/&/g, '&amp;')
            .replace(/</g, '&lt;')
            .replace(/>/g, '&gt;')
            .replace(/"/g, '&quot;')
            .replace(/'/g, '&#x27;')
            .replace(/\//g, '&#x2F;')
            .trim();
        
        // Type-specific validation
        switch (type) {
            case 'email':
                const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
                return emailRegex.test(sanitized) ? sanitized : null;
                
            case 'phone':
                const phoneRegex = /^(\+964|0)?7[0-9]{9}$/;
                return phoneRegex.test(sanitized.replace(/\s/g, '')) ? sanitized : null;
                
            case 'name':
                const nameRegex = /^[\u0600-\u06FFa-zA-Z\s]{2,50}$/;
                return nameRegex.test(sanitized) ? sanitized : null;
                
            case 'text':
                const minLength = options.minLength || 1;
                const maxLength = options.maxLength || 1000;
                return sanitized.length >= minLength && sanitized.length <= maxLength ? sanitized : null;
                
            default:
                return sanitized;
        }
    },
    
    // XSS Detection
    detectXSS: function(input) {
        const xssPatterns = [
            /<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi,
            /<iframe\b[^<]*(?:(?!<\/iframe>)<[^<]*)*<\/iframe>/gi,
            /javascript:/gi,
            /on\w+\s*=/gi,
            /eval\s*\(/gi
        ];
        
        return xssPatterns.some(pattern => pattern.test(input));
    },
    
    // Rate Limiter
    initializeRateLimiter: function() {
        this.rateLimiter = {
            requests: new Map(),
            
            isAllowed: function(key, maxRequests = 10, windowMs = 60000) {
                const now = Date.now();
                const windowStart = now - windowMs;
                
                if (!this.requests.has(key)) {
                    this.requests.set(key, []);
                }
                
                const requests = this.requests.get(key);
                const validRequests = requests.filter(timestamp => timestamp > windowStart);
                this.requests.set(key, validRequests);
                
                if (validRequests.length >= maxRequests) {
                    return false;
                }
                
                validRequests.push(now);
                return true;
            }
        };
    },
    
    // Check rate limit
    checkRateLimit: function(action, maxRequests = 5, windowMs = 300000) {
        return this.rateLimiter.isAllowed(action, maxRequests, windowMs);
    },
    
    // Setup security headers
    setupSecurityHeaders: function() {
        // Add security-related meta tags
        const securityMetas = [
            { name: 'X-Content-Type-Options', content: 'nosniff' },
            { name: 'X-Frame-Options', content: 'DENY' },
            { name: 'X-XSS-Protection', content: '1; mode=block' },
            { name: 'Referrer-Policy', content: 'strict-origin-when-cross-origin' }
        ];
        
        securityMetas.forEach(meta => {
            const element = document.createElement('meta');
            element.httpEquiv = meta.name;
            element.content = meta.content;
            document.head.appendChild(element);
        });
    },
    
    // Secure form submission
    secureFormSubmit: function(formData, action) {
        // Validate all form fields
        const validatedData = {};
        let isValid = true;
        
        for (const [key, value] of formData.entries()) {
            if (this.detectXSS(value)) {
                isValid = false;
                break;
            }
            
            validatedData[key] = this.validateInput(value, 'text');
        }
        
        if (!isValid) {
            throw new Error('Invalid or potentially malicious input detected');
        }
        
        // Check rate limit
        if (!this.checkRateLimit(action)) {
            throw new Error('Rate limit exceeded. Please try again later.');
        }
        
        return validatedData;
    },
    
    // Log security events
    logSecurityEvent: function(event, details) {
        try {
            const securityLogs = JSON.parse(localStorage.getItem('velasweets_security_logs') || '[]');
            securityLogs.push({
                event,
                details,
                timestamp: new Date().toISOString(),
                userAgent: navigator.userAgent.substring(0, 100)
            });
            
            // Keep only last 50 events
            if (securityLogs.length > 50) {
                securityLogs.splice(0, securityLogs.length - 50);
            }
            
            localStorage.setItem('velasweets_security_logs', JSON.stringify(securityLogs));
        } catch (error) {
            // Fail silently for security logs
        }
    }
};

// Register the module
if (typeof VelaSweets !== 'undefined') {
    VelaSweets.registerModule('security', SecurityModule);
}

// Export for global use
window.SecurityModule = SecurityModule;

// VelaSweets Store JavaScript

// Products data with local images and fallbacks
const products = {
    1: {
        id: 1,
        name: 'جوزية',
        price: 7000,
        image: './images/جوزية.jpg',
        fallbackImage: 'https://images.unsplash.com/photo-1578985545062-69928b1d9587?w=400&h=300&fit=crop&crop=center',
        description: 'قطع جوزية محشية ومغطاة بطبقة شوكولاتة فاخرة',
        category: 'حلويات',
        inStock: true,
        rating: 4.5,
        reviewCount: 23
    },
    2: {
        id: 2,
        name: 'مادلين كيك',
        price: 4500,
        image: './images/مادلين كيك.jpg',
        fallbackImage: 'https://images.unsplash.com/photo-1587668178277-295251f900ce?w=400&h=300&fit=crop&crop=center',
        description: 'كيك ناعم ومميز مغطى بشوكولاتة فاخرة ومزين بالبندق المحمص',
        category: 'كيك',
        inStock: true,
        rating: 4.2,
        reviewCount: 18
    },
    3: {
        id: 3,
        name: 'حلى ڤيلا',
        price: 6000,
        image: './images/حلى ڤيلا.jpg',
        fallbackImage: 'https://images.unsplash.com/photo-1551024506-0bccd828d307?w=400&h=300&fit=crop&crop=center',
        description: 'حلى فاخر بطبقات متعددة من الكريمة والفول السوداني المقرمش',
        category: 'حلى',
        inStock: true,
        rating: 4.8,
        reviewCount: 31
    }
};

// Image handling functions for better product display
function handleImageError(imgElement, productId) {
    const product = products[productId];
    if (product && product.fallbackImage) {
        imgElement.src = product.fallbackImage;
        imgElement.onerror = null; // Prevent infinite loop
        console.log(`تم تحميل الصورة البديلة للمنتج: ${product.name}`);
    }
}

function preloadImages() {
    Object.values(products).forEach(product => {
        const img = new Image();
        img.onload = () => {
            console.log(`تم تحميل صورة ${product.name} بنجاح`);
        };
        img.onerror = () => {
            console.warn(`فشل في تحميل صورة ${product.name}، سيتم استخدام الصورة البديلة`);
        };
        img.src = product.image;
    });
}

function optimizeImageLoading() {
    // Add lazy loading to all product images
    const images = document.querySelectorAll('.product-image');

    if ('IntersectionObserver' in window) {
        const imageObserver = new IntersectionObserver((entries, observer) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const img = entry.target;
                    img.src = img.dataset.src;
                    img.classList.remove('lazy');
                    img.classList.add('loaded');
                    observer.unobserve(img);
                }
            });
        });

        images.forEach(img => {
            if (img.dataset.src) {
                imageObserver.observe(img);
            }
        });
    } else {
        // Fallback for browsers without IntersectionObserver
        images.forEach(img => {
            if (img.dataset.src) {
                img.src = img.dataset.src;
                img.classList.remove('lazy');
                img.classList.add('loaded');
            }
        });
    }
}

// Enhanced product display functions
function renderProducts() {
    const productGrid = document.querySelector('.product-grid');
    if (!productGrid) return;

    productGrid.innerHTML = Object.values(products).map(product => `
        <div class="product-card" data-product-id="${product.id}">
            <div class="relative overflow-hidden">
                <img
                    src="${product.image}"
                    alt="${product.name}"
                    class="product-image"
                    onerror="handleImageError(this, ${product.id})"
                    loading="lazy"
                >
                <button class="absolute top-4 right-4 p-2 bg-white dark:bg-gray-800 rounded-full shadow-lg hover:bg-pink-50 dark:hover:bg-gray-700 transition-colors" onclick="toggleFavorite(${product.id})">
                    <i class="ri-heart-line text-pink-600 dark:text-pink-400"></i>
                </button>
                ${!product.inStock ? '<div class="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center"><span class="text-white font-bold">نفدت الكمية</span></div>' : ''}
            </div>
            <div class="p-6">
                <h3 class="text-xl font-semibold mb-2 text-gray-900 dark:text-white">${product.name}</h3>
                <p class="text-gray-600 dark:text-gray-300 mb-4">${product.description}</p>
                <div class="flex items-center justify-between mb-4">
                    <span class="price">${formatPrice(product.price)}</span>
                    <div class="flex items-center gap-1">
                        <div class="flex text-yellow-400 text-sm">
                            ${generateStars(product.rating)}
                        </div>
                        <span class="text-xs text-gray-500 dark:text-gray-400">(${product.reviewCount})</span>
                    </div>
                </div>
                <div class="flex gap-2">
                    <button onclick="openProductModal(${product.id})" class="btn-secondary flex-1" ${!product.inStock ? 'disabled' : ''}>
                        عرض التفاصيل
                    </button>
                    <button onclick="addToCart(${product.id})" class="btn-primary flex-1" ${!product.inStock ? 'disabled' : ''}>
                        ${product.inStock ? 'أضف للسلة' : 'نفدت الكمية'}
                    </button>
                </div>
            </div>
        </div>
    `).join('');

    // Update favorite buttons
    updateFavoriteButtons();
}

function generateStars(rating) {
    const fullStars = Math.floor(rating);
    const hasHalfStar = rating % 1 >= 0.5;
    let stars = '';

    for (let i = 0; i < 5; i++) {
        if (i < fullStars) {
            stars += '<i class="ri-star-fill"></i>';
        } else if (i === fullStars && hasHalfStar) {
            stars += '<i class="ri-star-half-line"></i>';
        } else {
            stars += '<i class="ri-star-line"></i>';
        }
    }

    return stars;
}

// Coupons removed as requested

// Global state - Clean start for real store
let cart = [];
let favorites = [];
let notifications = [];
let isDarkMode = JSON.parse(localStorage.getItem('velasweets_dark_mode')) || false;
let profiles = [];
let currentProfileId = null;
let currentLanguage = localStorage.getItem('velasweets_language') || 'ar';
// Coupon system removed

// Clean up any demo data on first load
function cleanDemoData() {
    // Clear any existing demo data
    localStorage.removeItem('velasweets_cart');
    localStorage.removeItem('velasweets_favorites');
    localStorage.removeItem('velasweets_notifications');
    localStorage.removeItem('velasweets_profiles');
    localStorage.removeItem('velasweets_current_profile');
    localStorage.removeItem('velasweets_orders');
    localStorage.removeItem('velasweets_analytics');
    localStorage.removeItem('velasweets_errors');
}

// DOM Elements - will be initialized after DOM loads
let cartBtn, favoritesBtn, notificationsBtn, cartSidebar, favoritesSidebar, overlay, themeToggle;
let cartCount, favoritesCount, notificationsCount, cartItems, favoritesItems, cartTotal;
let checkoutBtn, clearFavoritesBtn, productModal, notificationsDropdown, analyticsModal, privacyModal, checkoutModal;
let profileModal, ordersModal, mobileProfileModal;

// Initialize DOM elements
function initializeDOM() {
    cartBtn = document.getElementById('cart-btn');
    favoritesBtn = document.getElementById('favorites-btn');
    notificationsBtn = document.getElementById('notifications-btn');
    cartSidebar = document.getElementById('cart-sidebar');
    favoritesSidebar = document.getElementById('favorites-sidebar');
    overlay = document.getElementById('overlay');
    themeToggle = document.getElementById('theme-toggle');
    cartCount = document.getElementById('cart-count');
    favoritesCount = document.getElementById('favorites-count');
    notificationsCount = document.getElementById('notifications-count');
    cartItems = document.getElementById('cart-items');
    favoritesItems = document.getElementById('favorites-items');
    cartTotal = document.getElementById('cart-total');
    checkoutBtn = document.getElementById('checkout-btn');
    clearFavoritesBtn = document.getElementById('clear-favorites');
    productModal = document.getElementById('product-modal');
    notificationsDropdown = document.getElementById('notifications-dropdown');
    analyticsModal = document.getElementById('analytics-modal');
    privacyModal = document.getElementById('privacy-modal');
    checkoutModal = document.getElementById('checkout-modal');
    profileModal = document.getElementById('profile-modal');
    ordersModal = document.getElementById('orders-modal');
    mobileProfileModal = document.getElementById('mobile-profile-modal');
}

// Initialize app
function initializeApp() {
    // Initialize DOM elements
    initializeDOM();

    // Initialize theme
    initializeTheme();

    // Load saved data
    loadSavedData();

    // Render products with loading animation
    setTimeout(() => {
        renderProducts();
        preloadImages();
        optimizeImageLoading();
    }, 1000); // Show skeleton for 1 second

    // Setup event listeners
    setupEventListeners();

    // Initialize other components
    updateCartUI();
    updateFavoritesUI();
    updateNotificationsUI();

    // Request notification permission
    requestNotificationPermission();

    console.log('VelaSweets متجر تم تحميله بنجاح!');
}

// Load saved data from localStorage
function loadSavedData() {
    try {
        // Load cart
        const savedCart = localStorage.getItem('velasweets_cart');
        if (savedCart) {
            cart = JSON.parse(savedCart);
        }

        // Load favorites
        const savedFavorites = localStorage.getItem('velasweets_favorites');
        if (savedFavorites) {
            favorites = JSON.parse(savedFavorites);
        }

        // Load notifications
        const savedNotifications = localStorage.getItem('velasweets_notifications');
        if (savedNotifications) {
            notifications = JSON.parse(savedNotifications);
        }

        // Load profiles
        const savedProfiles = localStorage.getItem('velasweets_profiles');
        if (savedProfiles) {
            profiles = JSON.parse(savedProfiles);
        }

        // Load current profile
        const savedCurrentProfile = localStorage.getItem('velasweets_current_profile');
        if (savedCurrentProfile) {
            currentProfileId = savedCurrentProfile;
        }

    } catch (error) {
        console.error('خطأ في تحميل البيانات المحفوظة:', error);
        // Reset to defaults if data is corrupted
        cart = [];
        favorites = [];
        notifications = [];
        profiles = [];
        currentProfileId = null;
    }
}

// Theme Management
function initializeTheme() {
    if (isDarkMode) {
        document.documentElement.classList.add('dark');
    } else {
        document.documentElement.classList.remove('dark');
    }
}

function toggleTheme() {
    isDarkMode = !isDarkMode;
    localStorage.setItem('velasweets_dark_mode', JSON.stringify(isDarkMode));
    
    if (isDarkMode) {
        document.documentElement.classList.add('dark');
    } else {
        document.documentElement.classList.remove('dark');
    }
}

// Cart Management
function addToCart(productId, showLoading = true) {
    const product = products[productId];
    if (!product) return;

    // Show loading state
    if (showLoading) {
        const button = document.querySelector(`button[onclick="addToCart(${productId})"]`);
        if (button) {
            showButtonLoading(button);
        }
    }

    // Simulate async operation
    setTimeout(() => {
        const existingItem = cart.find(item => item.id === productId);

        if (existingItem) {
            existingItem.quantity += 1;
        } else {
            cart.push({
                id: productId,
                name: product.name,
                price: product.price,
                image: product.image,
                quantity: 1
            });
        }

        saveCart();
        updateCartUI();
        addNotification(`تم إضافة ${product.name} إلى السلة`, 'success');
        showEnhancedNotification('تم إضافة المنتج إلى السلة', 'success', 'ri-shopping-cart-line');

        // Record analytics activity
        addAnalyticsActivity('cart', `تم إضافة ${product.name} إلى السلة`);

        // Hide loading state
        if (showLoading) {
            const button = document.querySelector(`button[onclick="addToCart(${productId})"]`);
            if (button) {
                hideButtonLoading(button);
            }
        }

        // Add bounce animation to cart icon
        animateCartIcon();

    }, showLoading ? 800 : 0);
}

function removeFromCart(productId) {
    cart = cart.filter(item => item.id !== productId);
    saveCart();
    updateCartUI();
    showNotification('تم حذف المنتج من السلة', 'info');
}

function updateCartQuantity(productId, quantity) {
    const item = cart.find(item => item.id === productId);
    if (item) {
        if (quantity <= 0) {
            removeFromCart(productId);
        } else {
            item.quantity = quantity;
            saveCart();
            updateCartUI();
        }
    }
}

function saveCart() {
    localStorage.setItem('velasweets_cart', JSON.stringify(cart));
}

function updateCartUI() {
    // Update cart count
    const totalItems = cart.reduce((sum, item) => sum + item.quantity, 0);
    cartCount.textContent = totalItems;
    cartCount.classList.toggle('hidden', totalItems === 0);
    
    // Update cart items
    if (cart.length === 0) {
        cartItems.innerHTML = '<div class="text-center text-gray-500 dark:text-gray-400 py-8">السلة فارغة</div>';
        cartTotal.textContent = '0 د.ع';
        checkoutBtn.disabled = true;
    } else {
        cartItems.innerHTML = cart.map(item => `
            <div class="flex items-center space-x-4 space-x-reverse p-4 border-b border-gray-200 dark:border-gray-700">
                <img src="${item.image || 'https://images.unsplash.com/photo-1578985545062-69928b1d9587?w=100&h=100&fit=crop&crop=center'}" alt="${item.name}" class="w-16 h-16 object-cover rounded-lg" onerror="this.src='https://images.unsplash.com/photo-1578985545062-69928b1d9587?w=100&h=100&fit=crop&crop=center'">
                <div class="flex-1">
                    <h4 class="font-semibold text-gray-900 dark:text-white">${item.name}</h4>
                    <p class="text-pink-600 dark:text-pink-400 font-bold">${formatPrice(item.price)}</p>
                </div>
                <div class="flex items-center space-x-2 space-x-reverse">
                    <button onclick="updateCartQuantity(${item.id}, ${item.quantity - 1})" class="w-8 h-8 bg-gray-200 dark:bg-gray-600 rounded-full flex items-center justify-center hover:bg-gray-300 dark:hover:bg-gray-500 transition-colors">
                        <i class="ri-subtract-line text-sm"></i>
                    </button>
                    <span class="w-8 text-center font-semibold text-gray-900 dark:text-white">${item.quantity}</span>
                    <button onclick="updateCartQuantity(${item.id}, ${item.quantity + 1})" class="w-8 h-8 bg-gray-200 dark:bg-gray-600 rounded-full flex items-center justify-center hover:bg-gray-300 dark:hover:bg-gray-500 transition-colors">
                        <i class="ri-add-line text-sm"></i>
                    </button>
                </div>
                <button onclick="removeFromCart(${item.id})" class="text-red-500 hover:text-red-700 transition-colors">
                    <i class="ri-delete-bin-line"></i>
                </button>
            </div>
        `).join('');
        
        const total = cart.reduce((sum, item) => sum + (item.price * item.quantity), 0);
        cartTotal.textContent = formatPrice(total);
        checkoutBtn.disabled = false;
    }
}

// Favorites Management
function toggleFavorite(productId) {
    const product = products[productId];
    if (!product) return;

    const existingIndex = favorites.findIndex(item => item.id === productId);
    const heartButton = document.querySelector(`button[onclick="toggleFavorite(${productId})"] i`);

    // Add heart animation
    if (heartButton) {
        heartButton.classList.add('scale-in');
        setTimeout(() => heartButton.classList.remove('scale-in'), 300);
    }

    if (existingIndex > -1) {
        favorites.splice(existingIndex, 1);
        addNotification(`تم حذف ${product.name} من المفضلة`, 'info');
        showEnhancedNotification('تم حذف المنتج من المفضلة', 'info', 'ri-heart-line');

        // Animate heart icon
        if (heartButton) {
            heartButton.classList.remove('ri-heart-fill');
            heartButton.classList.add('ri-heart-line');
        }
    } else {
        favorites.push({
            id: productId,
            name: product.name,
            price: product.price,
            image: product.image
        });
        addNotification(`تم إضافة ${product.name} إلى المفضلة`, 'success');
        showEnhancedNotification('تم إضافة المنتج إلى المفضلة', 'success', 'ri-heart-fill');

        // Record analytics activity
        addAnalyticsActivity('favorite', `تم إضافة ${product.name} إلى المفضلة`);

        // Animate heart icon
        if (heartButton) {
            heartButton.classList.remove('ri-heart-line');
            heartButton.classList.add('ri-heart-fill');
            heartButton.style.color = '#ec4899';
        }
    }

    saveFavorites();
    updateFavoritesUI();
    updateFavoriteButtons();

    // Animate favorites icon in header
    if (favoritesBtn) {
        favoritesBtn.classList.add('bounce-in');
        setTimeout(() => favoritesBtn.classList.remove('bounce-in'), 600);
    }
}

function saveFavorites() {
    localStorage.setItem('velasweets_favorites', JSON.stringify(favorites));
}

function updateFavoritesUI() {
    // Update favorites count
    favoritesCount.textContent = favorites.length;
    favoritesCount.classList.toggle('hidden', favorites.length === 0);
    
    // Update favorites items
    if (favorites.length === 0) {
        favoritesItems.innerHTML = '<div class="text-center text-gray-500 dark:text-gray-400 py-8">لا توجد منتجات مفضلة</div>';
        clearFavoritesBtn.disabled = true;
    } else {
        favoritesItems.innerHTML = favorites.map(item => `
            <div class="flex items-center space-x-4 space-x-reverse p-4 border-b border-gray-200 dark:border-gray-700">
                <img src="${item.image}" alt="${item.name}" class="w-16 h-16 object-cover rounded-lg">
                <div class="flex-1">
                    <h4 class="font-semibold text-gray-900 dark:text-white">${item.name}</h4>
                    <p class="text-pink-600 dark:text-pink-400 font-bold">${formatPrice(item.price)}</p>
                </div>
                <div class="flex space-x-2 space-x-reverse">
                    <button onclick="addToCart(${item.id})" class="bg-pink-600 hover:bg-pink-700 text-white px-3 py-1 rounded text-sm transition-colors">
                        أضف للسلة
                    </button>
                    <button onclick="toggleFavorite(${item.id})" class="text-red-500 hover:text-red-700 transition-colors">
                        <i class="ri-heart-fill"></i>
                    </button>
                </div>
            </div>
        `).join('');
        clearFavoritesBtn.disabled = false;
    }
}

function updateFavoriteButtons() {
    document.querySelectorAll('[onclick^="toggleFavorite"]').forEach(button => {
        const productId = parseInt(button.getAttribute('onclick').match(/\d+/)[0]);
        const isFavorite = favorites.some(item => item.id === productId);
        const icon = button.querySelector('i');
        
        if (isFavorite) {
            icon.className = 'ri-heart-fill text-pink-600 dark:text-pink-400';
        } else {
            icon.className = 'ri-heart-line text-pink-600 dark:text-pink-400';
        }
    });
}

function clearFavorites() {
    favorites = [];
    saveFavorites();
    updateFavoritesUI();
    updateFavoriteButtons();
    showNotification('تم مسح جميع المفضلة', 'info');
}

// Enhanced Notifications Management
const notificationTypes = {
    orders: { icon: 'ri-shopping-bag-line', color: 'blue' },
    offers: { icon: 'ri-price-tag-3-line', color: 'green' },
    updates: { icon: 'ri-information-line', color: 'purple' },
    success: { icon: 'ri-check-line', color: 'green' },
    error: { icon: 'ri-error-warning-line', color: 'red' },
    warning: { icon: 'ri-alert-line', color: 'yellow' },
    info: { icon: 'ri-information-line', color: 'blue' }
};

let currentNotificationFilter = 'all';

function addNotification(message, type = 'info', category = 'updates') {
    const notification = {
        id: Date.now(),
        message: message,
        type: type,
        category: category,
        timestamp: new Date().toISOString(),
        read: false
    };

    notifications.unshift(notification);

    // Keep only last 100 notifications
    if (notifications.length > 100) {
        notifications = notifications.slice(0, 100);
    }

    saveNotifications();
    updateNotificationsUI();

    // Show browser notification if supported
    showBrowserNotification(message, type);
}

function showBrowserNotification(message, type = 'info') {
    if ('Notification' in window && Notification.permission === 'granted') {
        const notification = new Notification('VelaSweets', {
            body: message,
            icon: type === 'success' ? '✅' : type === 'error' ? '❌' : 'ℹ️',
            icon: '/favicon.ico',
            badge: '/favicon.ico'
        });

        setTimeout(() => notification.close(), 5000);
    }
}

function requestNotificationPermission() {
    if ('Notification' in window && Notification.permission === 'default') {
        Notification.requestPermission().then(permission => {
            if (permission === 'granted') {
                showEnhancedNotification('تم تفعيل الإشعارات بنجاح!', 'success', 'ri-notification-line');
            }
        });
    }
}

function toggleNotificationsDropdown() {
    if (!notificationsDropdown) return;

    const isHidden = notificationsDropdown.classList.contains('hidden');

    if (isHidden) {
        notificationsDropdown.classList.remove('hidden');
        notificationsDropdown.classList.add('scale-in');
        renderNotificationsList();

        // Close dropdown when clicking outside
        setTimeout(() => {
            document.addEventListener('click', closeNotificationsOnOutsideClick);
        }, 100);
    } else {
        closeNotificationsDropdown();
    }
}

function closeNotificationsDropdown() {
    if (!notificationsDropdown) return;

    notificationsDropdown.classList.add('hidden');
    notificationsDropdown.classList.remove('scale-in');
    document.removeEventListener('click', closeNotificationsOnOutsideClick);
}

function closeNotificationsOnOutsideClick(event) {
    if (!notificationsDropdown.contains(event.target) && !notificationsBtn.contains(event.target)) {
        closeNotificationsDropdown();
    }
}

function filterNotifications(filter) {
    currentNotificationFilter = filter;

    // Update active tab
    document.querySelectorAll('.notification-tab').forEach(tab => {
        tab.classList.remove('active', 'border-pink-600', 'text-pink-600');
        tab.classList.add('border-transparent', 'text-gray-500');
    });

    const activeTab = document.querySelector(`[data-filter="${filter}"]`);
    if (activeTab) {
        activeTab.classList.add('active', 'border-pink-600', 'text-pink-600');
        activeTab.classList.remove('border-transparent', 'text-gray-500');
    }

    renderNotificationsList();
}

function markNotificationAsRead(notificationId) {
    const notification = notifications.find(n => n.id === notificationId);
    if (notification && !notification.read) {
        notification.read = true;
        saveNotifications();
        updateNotificationsUI();
        renderNotificationsList();
    }
}

function markAllNotificationsAsRead() {
    notifications.forEach(n => n.read = true);
    saveNotifications();
    updateNotificationsUI();
    renderNotificationsList();
    showEnhancedNotification('تم تحديد جميع الإشعارات كمقروءة', 'success', 'ri-check-line');
}

function removeNotification(notificationId) {
    notifications = notifications.filter(n => n.id !== notificationId);
    saveNotifications();
    updateNotificationsUI();
    renderNotificationsList();

    // Keep notifications dropdown open after deletion
    const notificationsDropdown = document.getElementById('notifications-dropdown');
    if (notificationsDropdown && !notificationsDropdown.classList.contains('hidden')) {
        // Dropdown stays open - no action needed
        showEnhancedNotification('تم حذف الإشعار', 'success', 'ri-delete-bin-line');
    }
}

async function clearAllNotifications() {
    if (notifications.length === 0) return;

    const confirmed = await customConfirm('هل أنت متأكد من حذف جميع الإشعارات؟', 'تأكيد الحذف');
    if (confirmed) {
        notifications = [];
        saveNotifications();
        updateNotificationsUI();
        renderNotificationsList();
        showEnhancedNotification('تم حذف جميع الإشعارات', 'success', 'ri-delete-bin-line');
    }
}

function renderNotificationsList() {
    const notificationsList = document.getElementById('notifications-list');
    if (!notificationsList) return;

    let filteredNotifications = notifications;

    if (currentNotificationFilter !== 'all') {
        filteredNotifications = notifications.filter(n => n.category === currentNotificationFilter);
    }

    if (filteredNotifications.length === 0) {
        notificationsList.innerHTML = `
            <div class="p-8 text-center text-gray-500 dark:text-gray-400">
                <i class="ri-notification-off-line text-4xl mb-4"></i>
                <p>لا توجد إشعارات</p>
            </div>
        `;
        return;
    }

    notificationsList.innerHTML = filteredNotifications.map(notification => {
        const typeInfo = notificationTypes[notification.type] || notificationTypes.info;
        const timeAgo = getTimeAgo(new Date(notification.timestamp));

        return `
            <div class="notification-item p-4 border-b border-gray-200 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors ${!notification.read ? 'bg-pink-50 dark:bg-pink-900/10' : ''}" data-id="${notification.id}">
                <div class="flex items-start gap-3">
                    <div class="flex-shrink-0 w-10 h-10 bg-${typeInfo.color}-100 dark:bg-${typeInfo.color}-900/20 rounded-full flex items-center justify-center">
                        <i class="${typeInfo.icon} text-${typeInfo.color}-600 dark:text-${typeInfo.color}-400"></i>
                    </div>
                    <div class="flex-1 min-w-0">
                        <p class="text-sm text-gray-900 dark:text-white ${!notification.read ? 'font-semibold' : ''}">${notification.message}</p>
                        <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">${timeAgo}</p>
                    </div>
                    <div class="flex items-center gap-2">
                        ${!notification.read ? '<div class="w-2 h-2 bg-pink-600 rounded-full"></div>' : ''}
                        <button onclick="removeNotification(${notification.id})" class="text-gray-400 hover:text-red-600 transition-colors">
                            <i class="ri-close-line"></i>
                        </button>
                    </div>
                </div>
            </div>
        `;
    }).join('');

    // Add click handlers to mark as read
    notificationsList.querySelectorAll('.notification-item').forEach(item => {
        item.addEventListener('click', function() {
            const notificationId = parseInt(this.dataset.id);
            markNotificationAsRead(notificationId);
        });
    });
}

function saveNotifications() {
    localStorage.setItem('velasweets_notifications', JSON.stringify(notifications));
}

function updateNotificationsUI() {
    const unreadCount = notifications.filter(n => !n.read).length;
    notificationsCount.textContent = unreadCount;
    notificationsCount.classList.toggle('hidden', unreadCount === 0);
}

// Enhanced notification system - old function removed

function getNotificationIcon(type) {
    switch (type) {
        case 'success': return '✅';
        case 'error': return '❌';
        case 'warning': return '⚠️';
        case 'info': return 'ℹ️';
        default: return '📢';
    }
}

function getTimeAgo(date) {
    const now = new Date();
    const diffInSeconds = Math.floor((now - date) / 1000);

    if (diffInSeconds < 60) return 'الآن';
    if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)} دقيقة`;
    if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)} ساعة`;
    return `${Math.floor(diffInSeconds / 86400)} يوم`;
}

// UI Management
function openCart() {
    if (!cartSidebar || !overlay) return;

    // Add slide animation
    cartSidebar.classList.add('slide-in-left');
    cartSidebar.classList.remove('-translate-x-full');
    overlay.classList.remove('hidden');
    document.body.style.overflow = 'hidden';

    // Remove animation class after animation completes
    setTimeout(() => {
        cartSidebar.classList.remove('slide-in-left');
    }, 500);
}

function closeCart() {
    if (!cartSidebar || !overlay) return;

    cartSidebar.classList.add('-translate-x-full');
    overlay.classList.add('hidden');
    document.body.style.overflow = 'auto';
}

function openFavorites() {
    if (!favoritesSidebar || !overlay) return;

    // Add slide animation
    favoritesSidebar.classList.add('slide-in-right');
    favoritesSidebar.classList.remove('translate-x-full');
    overlay.classList.remove('hidden');
    document.body.style.overflow = 'hidden';

    // Remove animation class after animation completes
    setTimeout(() => {
        favoritesSidebar.classList.remove('slide-in-right');
    }, 500);
}

function closeFavorites() {
    if (!favoritesSidebar || !overlay) return;

    favoritesSidebar.classList.add('translate-x-full');
    overlay.classList.add('hidden');
    document.body.style.overflow = 'auto';
}

// Event Listeners
function setupEventListeners() {
    // Theme toggle
    if (themeToggle) {
        themeToggle.addEventListener('click', toggleTheme);
    }

    // Cart
    if (cartBtn) {
        cartBtn.addEventListener('click', openCart);
    }
    const closeCartBtn = document.getElementById('close-cart');
    if (closeCartBtn) {
        closeCartBtn.addEventListener('click', closeCart);
    }

    // Favorites
    if (favoritesBtn) {
        favoritesBtn.addEventListener('click', openFavorites);
    }
    const closeFavoritesBtn = document.getElementById('close-favorites');
    if (closeFavoritesBtn) {
        closeFavoritesBtn.addEventListener('click', closeFavorites);
    }

    // Clear favorites
    if (clearFavoritesBtn) {
        clearFavoritesBtn.addEventListener('click', clearFavorites);
    }

    // Notifications
    if (notificationsBtn) {
        notificationsBtn.addEventListener('click', toggleNotificationsDropdown);

        // Close notifications dropdown when clicking outside
        document.addEventListener('click', function(e) {
            const notificationsDropdown = document.getElementById('notifications-dropdown');
            if (notificationsDropdown && !notificationsDropdown.classList.contains('hidden')) {
                // Check if click is outside notifications area
                if (!notificationsBtn.contains(e.target) && !notificationsDropdown.contains(e.target)) {
                    notificationsDropdown.classList.add('hidden');
                }
            }
        });
    }

    // Notification tabs
    document.querySelectorAll('.notification-tab').forEach(tab => {
        tab.addEventListener('click', function() {
            filterNotifications(this.dataset.filter);
        });
    });

    // Mark all as read
    const markAllReadBtn = document.getElementById('mark-all-read');
    if (markAllReadBtn) {
        markAllReadBtn.addEventListener('click', markAllNotificationsAsRead);
    }

    // Close notifications dropdown
    const closeNotificationsBtn = document.getElementById('close-notifications');
    if (closeNotificationsBtn) {
        closeNotificationsBtn.addEventListener('click', function() {
            const notificationsDropdown = document.getElementById('notifications-dropdown');
            if (notificationsDropdown) {
                notificationsDropdown.classList.add('hidden');
            }
        });
    }

    // Clear all notifications
    const clearAllBtn = document.getElementById('clear-all-notifications');
    if (clearAllBtn) {
        clearAllBtn.addEventListener('click', clearAllNotifications);
    }

    // Notifications settings
    const notificationsSettingsBtn = document.getElementById('notifications-settings');
    if (notificationsSettingsBtn) {
        notificationsSettingsBtn.addEventListener('click', requestNotificationPermission);
    }

    // Analytics removed from public interface

    // Privacy Policy
    const privacyPolicyBtn = document.getElementById('privacy-policy-btn');
    if (privacyPolicyBtn) {
        privacyPolicyBtn.addEventListener('click', () => openPrivacyModal('privacy-policy'));
    }

    const termsOfServiceBtn = document.getElementById('terms-of-service-btn');
    if (termsOfServiceBtn) {
        termsOfServiceBtn.addEventListener('click', () => openPrivacyModal('terms-of-service'));
    }

    const cookiePolicyBtn = document.getElementById('cookie-policy-btn');
    if (cookiePolicyBtn) {
        cookiePolicyBtn.addEventListener('click', () => openPrivacyModal('cookie-policy'));
    }

    const closePrivacyBtn = document.getElementById('close-privacy-modal');
    if (closePrivacyBtn) {
        closePrivacyBtn.addEventListener('click', closePrivacyModal);
    }

    // Close privacy modal when clicking outside
    if (privacyModal) {
        privacyModal.addEventListener('click', function(e) {
            if (e.target === privacyModal) {
                closePrivacyModal();
            }
        });
    }

    // Profile Menu Dropdown
    const profileMenuBtn = document.getElementById('profile-menu-btn');
    const profileDropdown = document.getElementById('profile-dropdown');

    if (profileMenuBtn && profileDropdown) {
        profileMenuBtn.addEventListener('click', function(e) {
            e.stopPropagation();
            toggleProfileDropdown();
        });

        // Close dropdown when clicking outside
        document.addEventListener('click', function(e) {
            if (!profileDropdown.contains(e.target) && !profileMenuBtn.contains(e.target)) {
                closeProfileDropdown();
            }
        });
    }

    // Profile Management
    const profileBtn = document.getElementById('profile-btn');
    if (profileBtn) {
        profileBtn.addEventListener('click', function() {
            closeProfileDropdown();
            openProfileModal();
        });
    }

    const closeProfileBtn = document.getElementById('close-profile-modal');
    if (closeProfileBtn) {
        closeProfileBtn.addEventListener('click', closeProfileModal);
    }

    const addProfileBtn = document.getElementById('add-profile-btn');
    if (addProfileBtn) {
        addProfileBtn.addEventListener('click', clearProfileForm);
    }

    const profileForm = document.getElementById('profile-form');
    if (profileForm) {
        profileForm.addEventListener('submit', handleProfileSubmit);
    }

    // Phone number validation
    const phoneInputs = document.querySelectorAll('#profile-phone1, #profile-phone2');
    phoneInputs.forEach(input => {
        input.addEventListener('input', function(e) {
            // Remove non-digits
            e.target.value = e.target.value.replace(/[^0-9]/g, '');

            // Limit to 11 digits
            if (e.target.value.length > 11) {
                e.target.value = e.target.value.slice(0, 11);
            }
        });
    });

    // Close profile modal when clicking outside
    if (profileModal) {
        profileModal.addEventListener('click', function(e) {
            if (e.target === profileModal) {
                closeProfileModal();
            }
        });
    }

    // Orders Tracking
    const ordersBtn = document.getElementById('orders-btn');
    if (ordersBtn) {
        ordersBtn.addEventListener('click', function() {
            closeProfileDropdown();
            openOrdersModal();
        });
    }

    const closeOrdersBtn = document.getElementById('close-orders-modal');
    if (closeOrdersBtn) {
        closeOrdersBtn.addEventListener('click', closeOrdersModal);
    }

    // Close orders modal when clicking outside
    if (ordersModal) {
        ordersModal.addEventListener('click', function(e) {
            if (e.target === ordersModal) {
                closeOrdersModal();
            }
        });
    }

    // Language Selector
    const languageBtn = document.getElementById('language-btn');
    const languageDropdown = document.getElementById('language-dropdown');

    if (languageBtn && languageDropdown) {
        languageBtn.addEventListener('click', function(e) {
            e.stopPropagation();
            toggleLanguageDropdown();
        });

        // Close dropdown when clicking outside
        document.addEventListener('click', function(e) {
            if (!languageDropdown.contains(e.target) && !languageBtn.contains(e.target)) {
                closeLanguageDropdown();
            }
        });
    }

    // Language options
    document.querySelectorAll('.language-option').forEach(option => {
        option.addEventListener('click', function() {
            changeLanguage(this.dataset.lang);
        });
    });

    // Overlay
    if (overlay) {
        overlay.addEventListener('click', function() {
            // Check which sidebar is open and close it
            if (cartSidebar && !cartSidebar.classList.contains('-translate-x-full')) {
                closeCart();
            }
            if (favoritesSidebar && !favoritesSidebar.classList.contains('translate-x-full')) {
                closeFavorites();
            }
        });
    }

    // Checkout
    if (checkoutBtn) {
        checkoutBtn.addEventListener('click', openCheckoutModal);
    }

    // Checkout modal
    const closeCheckoutBtn = document.getElementById('close-checkout-modal');
    if (closeCheckoutBtn) {
        closeCheckoutBtn.addEventListener('click', closeCheckoutModal);
    }

    const checkoutForm = document.getElementById('checkout-form');
    if (checkoutForm) {
        checkoutForm.addEventListener('submit', handleCheckoutSubmit);
    }

    // Payment method: Cash on delivery only

    // Close checkout modal when clicking outside
    if (checkoutModal) {
        checkoutModal.addEventListener('click', function(e) {
            if (e.target === checkoutModal) {
                closeCheckoutModal();
            }
        });
    }

    // Contact form
    const contactForm = document.getElementById('contact-form');
    if (contactForm) {
        contactForm.addEventListener('submit', handleContactForm);
    }

    // Product Modal
    const closeModalBtn = document.getElementById('close-product-modal');
    if (closeModalBtn) {
        closeModalBtn.addEventListener('click', closeProductModal);
    }

    const decreaseBtn = document.getElementById('decrease-quantity');
    if (decreaseBtn) {
        decreaseBtn.addEventListener('click', () => updateModalQuantity(-1));
    }

    const increaseBtn = document.getElementById('increase-quantity');
    if (increaseBtn) {
        increaseBtn.addEventListener('click', () => updateModalQuantity(1));
    }

    const modalAddToCartBtn = document.getElementById('modal-add-to-cart');
    if (modalAddToCartBtn) {
        modalAddToCartBtn.addEventListener('click', addModalProductToCart);
    }

    const modalAddToFavBtn = document.getElementById('modal-add-to-favorites');
    if (modalAddToFavBtn) {
        modalAddToFavBtn.addEventListener('click', toggleModalFavorite);
    }

    // Rating stars
    const ratingStars = document.querySelectorAll('#rating-stars i');
    ratingStars.forEach((star, index) => {
        star.addEventListener('click', () => setRating(index + 1));
        star.addEventListener('mouseenter', () => {
            // Highlight stars on hover
            ratingStars.forEach((s, i) => {
                if (i <= index) {
                    s.classList.add('text-yellow-400');
                } else {
                    s.classList.remove('text-yellow-400');
                }
            });
        });
    });

    // Submit review
    const submitReviewBtn = document.getElementById('submit-review');
    if (submitReviewBtn) {
        submitReviewBtn.addEventListener('click', submitReview);
    }

    // Close modal when clicking outside
    if (productModal) {
        productModal.addEventListener('click', function(e) {
            if (e.target === productModal) {
                closeProductModal();
            }
        });
    }

    // Coupon system removed

    // Escape key
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            closeCart();
            closeFavorites();
            closeProductModal();
        }
    });
}

// Utility Functions
function formatPrice(price) {
    return new Intl.NumberFormat('ar-IQ', {
        style: 'decimal',
        minimumFractionDigits: 0
    }).format(price) + ' د.ع';
}

function scrollToSection(sectionId) {
    const section = document.getElementById(sectionId);
    if (section) {
        section.scrollIntoView({ behavior: 'smooth' });
    }
}

function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    notification.innerHTML = `
        <div class="flex items-center">
            <i class="ri-information-line ml-2"></i>
            <span>${message}</span>
        </div>
    `;
    
    document.body.appendChild(notification);
    
    setTimeout(() => {
        notification.classList.add('show');
    }, 100);
    
    setTimeout(() => {
        notification.classList.remove('show');
        setTimeout(() => {
            document.body.removeChild(notification);
        }, 300);
    }, 3000);
}

// Order Form (placeholder)
function showOrderForm() {
    showNotification('سيتم إضافة نموذج الطلب قريباً', 'info');
}

// Enhanced Contact Form Handler with Security
function handleContactForm(e) {
    e.preventDefault();

    try {
        const formData = new FormData(e.target);
        const name = sanitizeInput(formData.get('name') || '');
        const email = sanitizeInput(formData.get('email') || '');
        const message = sanitizeInput(formData.get('message') || '');

        // Validation
        if (!name.trim()) {
            showEnhancedNotification('يرجى إدخال الاسم', 'error', 'ri-error-warning-line');
            return;
        }

        if (!email.trim() || !validateEmail(email)) {
            showEnhancedNotification('يرجى إدخال بريد إلكتروني صحيح', 'error', 'ri-error-warning-line');
            return;
        }

        if (!message.trim() || message.length < 10) {
            showEnhancedNotification('يرجى إدخال رسالة لا تقل عن 10 أحرف', 'error', 'ri-error-warning-line');
            return;
        }

        // Rate limiting
        if (!rateLimiter.isAllowed('contact-form', 3, 300000)) { // 3 requests per 5 minutes
            showEnhancedNotification('تم إرسال عدد كبير من الرسائل. يرجى المحاولة لاحقاً', 'warning', 'ri-time-line');
            return;
        }

        // Simulate form submission
        const contactData = {
            name: name,
            email: email,
            message: message,
            timestamp: new Date().toISOString(),
            userAgent: navigator.userAgent
        };

        // Store contact form submissions
        const contacts = JSON.parse(localStorage.getItem('velasweets_contacts')) || [];
        contacts.unshift(contactData);

        // Keep only last 100 contacts
        if (contacts.length > 100) {
            contacts.splice(100);
        }

        localStorage.setItem('velasweets_contacts', JSON.stringify(contacts));

        // Record analytics activity
        addAnalyticsActivity('contact', `تم إرسال رسالة جديدة من ${name}`);

        showEnhancedNotification('تم إرسال رسالتك بنجاح، سنتواصل معك قريباً', 'success', 'ri-check-line');
        e.target.reset();

    } catch (error) {
        handleError(error, 'handleContactForm');
    }
}

// Product Modal Functions
let currentModalProduct = null;
let modalQuantity = 1;
let selectedRating = 0;

function openProductModal(productId) {
    const product = products[productId];
    if (!product || !productModal) return;

    currentModalProduct = product;
    modalQuantity = 1;

    // Update modal content
    document.getElementById('modal-product-name').textContent = product.name;
    document.getElementById('modal-product-price').textContent = formatPrice(product.price);
    document.getElementById('modal-product-description').textContent = product.description;
    document.getElementById('modal-main-image').src = product.image;
    document.getElementById('modal-main-image').alt = product.name;
    document.getElementById('modal-quantity').textContent = modalQuantity;

    // Update rating display
    updateProductRating(productId);

    // Update options
    const optionsContainer = document.getElementById('modal-product-options');
    if (product.options && product.options.length > 0) {
        optionsContainer.innerHTML = `
            <div>
                <h4 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">الخيارات المتاحة</h4>
                <div class="flex flex-wrap gap-2">
                    ${product.options.map(option => `
                        <button class="px-4 py-2 border-2 border-pink-600 text-pink-600 hover:bg-pink-600 hover:text-white rounded-lg transition-colors text-sm option-btn" data-option="${option}">
                            ${option}
                        </button>
                    `).join('')}
                </div>
            </div>
        `;

        // Add click handlers for options
        optionsContainer.querySelectorAll('.option-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                // Remove active class from all buttons
                optionsContainer.querySelectorAll('.option-btn').forEach(b => {
                    b.classList.remove('bg-pink-600', 'text-white');
                    b.classList.add('text-pink-600');
                });
                // Add active class to clicked button
                this.classList.add('bg-pink-600', 'text-white');
                this.classList.remove('text-pink-600');
            });
        });
    } else {
        optionsContainer.innerHTML = '';
    }

    // Show modal with animation
    productModal.classList.remove('hidden');
    productModal.classList.add('scale-in');
    document.body.style.overflow = 'hidden';

    // Remove animation class after animation completes
    setTimeout(() => {
        productModal.classList.remove('scale-in');
    }, 300);
}

function closeProductModal() {
    if (!productModal) return;
    productModal.classList.add('hidden');
    document.body.style.overflow = 'auto';
    currentModalProduct = null;
    modalQuantity = 1;
    selectedRating = 0;
}

function updateModalQuantity(change) {
    modalQuantity = Math.max(1, modalQuantity + change);
    document.getElementById('modal-quantity').textContent = modalQuantity;
}

function addModalProductToCart() {
    if (!currentModalProduct) return;

    // Add multiple quantities
    for (let i = 0; i < modalQuantity; i++) {
        addToCart(currentModalProduct.id);
    }

    closeProductModal();
    showNotification(`تم إضافة ${modalQuantity} من ${currentModalProduct.name} إلى السلة`, 'success');
}

function toggleModalFavorite() {
    if (!currentModalProduct) return;
    toggleFavorite(currentModalProduct.id);

    // Update button appearance
    const btn = document.getElementById('modal-add-to-favorites');
    const icon = btn.querySelector('i');
    const isFavorite = favorites.some(fav => fav.id === currentModalProduct.id);

    if (isFavorite) {
        icon.classList.remove('ri-heart-line');
        icon.classList.add('ri-heart-fill');
        btn.classList.add('bg-pink-600', 'text-white');
        btn.classList.remove('text-pink-600');
    } else {
        icon.classList.remove('ri-heart-fill');
        icon.classList.add('ri-heart-line');
        btn.classList.remove('bg-pink-600', 'text-white');
        btn.classList.add('text-pink-600');
    }
}

// Rating System
function setRating(rating) {
    selectedRating = rating;
    const stars = document.querySelectorAll('#rating-stars i');
    stars.forEach((star, index) => {
        if (index < rating) {
            star.classList.remove('ri-star-line');
            star.classList.add('ri-star-fill', 'text-yellow-400');
        } else {
            star.classList.remove('ri-star-fill', 'text-yellow-400');
            star.classList.add('ri-star-line');
        }
    });
}

function submitReview() {
    try {
        const reviewTextElement = document.getElementById('review-text');
        const reviewText = sanitizeInput(reviewTextElement.value.trim());

        if (selectedRating === 0) {
            showEnhancedNotification('يرجى اختيار تقييم أولاً', 'error', 'ri-star-line');
            return;
        }

        if (reviewText === '') {
            showEnhancedNotification('يرجى كتابة مراجعة', 'error', 'ri-edit-line');
            return;
        }

        if (reviewText.length < 5) {
            showEnhancedNotification('يرجى كتابة مراجعة لا تقل عن 5 أحرف', 'error', 'ri-edit-line');
            return;
        }

        if (reviewText.length > 500) {
            showEnhancedNotification('المراجعة طويلة جداً (الحد الأقصى 500 حرف)', 'error', 'ri-edit-line');
            return;
        }

        // Rate limiting for reviews
        if (!rateLimiter.isAllowed(`review-${currentModalProduct.id}`, 1, 3600000)) { // 1 review per hour per product
            showEnhancedNotification('يمكنك إضافة مراجعة واحدة فقط كل ساعة لنفس المنتج', 'warning', 'ri-time-line');
            return;
        }

        // Add review to local storage (in a real app, this would go to a server)
        const review = {
            id: Date.now(),
            productId: currentModalProduct.id,
            rating: selectedRating,
            text: reviewText,
            date: new Date().toISOString(),
            author: 'مستخدم مجهول', // In a real app, this would be the logged-in user
            verified: false, // In a real app, this would be based on purchase history
            helpful: 0,
            reported: false
        };

        // Save review
        let reviews = JSON.parse(localStorage.getItem('velasweets_reviews')) || [];
        reviews.push(review);
        localStorage.setItem('velasweets_reviews', JSON.stringify(reviews));

        // Reset form
        reviewTextElement.value = '';
        setRating(0);

        showEnhancedNotification('تم إرسال تقييمك بنجاح، شكراً لك!', 'success', 'ri-check-line');

        // Record analytics activity
        addAnalyticsActivity('review', `تم إضافة تقييم ${selectedRating} نجوم لـ ${currentModalProduct.name}`);

        // Refresh reviews display
        loadProductReviews(currentModalProduct.id);
        updateProductRating(currentModalProduct.id);

    } catch (error) {
        handleError(error, 'submitReview');
    }
}

// Reviews Management
function getProductReviews(productId) {
    const reviews = JSON.parse(localStorage.getItem('velasweets_reviews')) || [];
    return reviews.filter(review => review.productId === productId);
}

function calculateAverageRating(productId) {
    const reviews = getProductReviews(productId);
    if (reviews.length === 0) return 0;

    const totalRating = reviews.reduce((sum, review) => sum + review.rating, 0);
    return (totalRating / reviews.length).toFixed(1);
}

function updateProductRating(productId) {
    const averageRating = calculateAverageRating(productId);
    const reviewCount = getProductReviews(productId).length;

    // Update modal rating display
    const modalRating = document.getElementById('modal-product-rating');
    if (modalRating) {
        const stars = modalRating.querySelectorAll('i');
        const fullStars = Math.floor(averageRating);
        const hasHalfStar = averageRating % 1 >= 0.5;

        stars.forEach((star, index) => {
            star.classList.remove('ri-star-fill', 'ri-star-half-line', 'ri-star-line');
            if (index < fullStars) {
                star.classList.add('ri-star-fill');
            } else if (index === fullStars && hasHalfStar) {
                star.classList.add('ri-star-half-line');
            } else {
                star.classList.add('ri-star-line');
            }
        });

        // Update rating text
        const ratingText = modalRating.nextElementSibling;
        if (ratingText) {
            ratingText.textContent = `(${averageRating}) - ${reviewCount} تقييم`;
        }
    }

    // Load and display reviews
    loadProductReviews(productId);
}

function loadProductReviews(productId) {
    const reviews = getProductReviews(productId);
    const reviewsList = document.getElementById('reviews-list');

    if (!reviewsList) return;

    if (reviews.length === 0) {
        reviewsList.innerHTML = `
            <div class="text-center py-8 text-gray-500 dark:text-gray-400">
                <i class="ri-chat-3-line text-4xl mb-4"></i>
                <p>لا توجد مراجعات بعد. كن أول من يقيم هذا المنتج!</p>
            </div>
        `;
        return;
    }

    // Sort reviews by date (newest first)
    reviews.sort((a, b) => new Date(b.date) - new Date(a.date));

    reviewsList.innerHTML = reviews.map(review => {
        const date = new Date(review.date);
        const timeAgo = getTimeAgo(date);
        const stars = Array.from({length: 5}, (_, i) =>
            i < review.rating ? 'ri-star-fill' : 'ri-star-line'
        ).map(starClass => `<i class="${starClass}"></i>`).join('');

        return `
            <div class="bg-white dark:bg-gray-700 rounded-xl p-6 border border-gray-200 dark:border-gray-600">
                <div class="flex items-center justify-between mb-3">
                    <div class="flex items-center gap-3">
                        <div class="w-10 h-10 bg-pink-100 dark:bg-pink-900 rounded-full flex items-center justify-center">
                            <i class="ri-user-line text-pink-600 dark:text-pink-400"></i>
                        </div>
                        <div>
                            <h6 class="font-semibold text-gray-900 dark:text-white">${review.author}</h6>
                            <div class="flex text-yellow-400 text-sm">
                                ${stars}
                            </div>
                        </div>
                    </div>
                    <span class="text-sm text-gray-500 dark:text-gray-400">${timeAgo}</span>
                </div>
                <p class="text-gray-600 dark:text-gray-300">${review.text}</p>
            </div>
        `;
    }).join('');
}

function getTimeAgo(date) {
    const now = new Date();
    const diffInSeconds = Math.floor((now - date) / 1000);

    if (diffInSeconds < 60) return 'منذ لحظات';
    if (diffInSeconds < 3600) return `منذ ${Math.floor(diffInSeconds / 60)} دقيقة`;
    if (diffInSeconds < 86400) return `منذ ${Math.floor(diffInSeconds / 3600)} ساعة`;
    if (diffInSeconds < 2592000) return `منذ ${Math.floor(diffInSeconds / 86400)} يوم`;
    return `منذ ${Math.floor(diffInSeconds / 2592000)} شهر`;
}

function updateMainPageRatings() {
    // Update ratings for each product on the main page
    Object.keys(products).forEach(productId => {
        const averageRating = calculateAverageRating(parseInt(productId));
        const reviewCount = getProductReviews(parseInt(productId)).length;

        // Find the product card and update its rating
        const productCards = document.querySelectorAll('.grid .bg-white, .grid .dark\\:bg-gray-800');
        productCards.forEach((card, index) => {
            if (index + 1 == productId) {
                const ratingDiv = card.querySelector('.flex.text-yellow-400');
                if (ratingDiv) {
                    const stars = ratingDiv.querySelectorAll('i');
                    const fullStars = Math.floor(averageRating);
                    const hasHalfStar = averageRating % 1 >= 0.5;

                    stars.forEach((star, starIndex) => {
                        star.classList.remove('ri-star-fill', 'ri-star-half-line', 'ri-star-line');
                        if (starIndex < fullStars) {
                            star.classList.add('ri-star-fill');
                        } else if (starIndex === fullStars && hasHalfStar) {
                            star.classList.add('ri-star-half-line');
                        } else {
                            star.classList.add('ri-star-line');
                        }
                    });

                    // Add review count if it doesn't exist
                    let reviewCountSpan = ratingDiv.parentElement.querySelector('.review-count');
                    if (!reviewCountSpan && reviewCount > 0) {
                        reviewCountSpan = document.createElement('span');
                        reviewCountSpan.className = 'review-count text-xs text-gray-500 dark:text-gray-400';
                        reviewCountSpan.textContent = `(${reviewCount})`;
                        ratingDiv.parentElement.appendChild(reviewCountSpan);
                    } else if (reviewCountSpan) {
                        reviewCountSpan.textContent = `(${reviewCount})`;
                    }
                }
            }
        });
    });
}

// Coupon system removed as requested

// Enhanced UX Functions
function showButtonLoading(button) {
    if (!button) return;

    button.disabled = true;
    button.classList.add('btn-loading');
    button.setAttribute('data-original-text', button.textContent);
}

function hideButtonLoading(button) {
    if (!button) return;

    button.disabled = false;
    button.classList.remove('btn-loading');
    const originalText = button.getAttribute('data-original-text');
    if (originalText) {
        button.textContent = originalText;
        button.removeAttribute('data-original-text');
    }
}

function animateCartIcon() {
    if (!cartBtn) return;

    cartBtn.classList.add('bounce-in');
    setTimeout(() => {
        cartBtn.classList.remove('bounce-in');
    }, 600);
}

function showEnhancedNotification(message, type = 'info', icon = 'ri-information-line') {
    const notification = document.createElement('div');
    notification.className = `fixed top-4 left-4 bg-white dark:bg-gray-800 border-r-4 p-4 rounded-lg shadow-lg z-50 transform -translate-x-full transition-all duration-300 slide-in-left max-w-sm`;

    // Set border color based on type
    const borderColors = {
        success: 'border-green-500',
        error: 'border-red-500',
        warning: 'border-yellow-500',
        info: 'border-blue-500'
    };

    const textColors = {
        success: 'text-green-800 dark:text-green-200',
        error: 'text-red-800 dark:text-red-200',
        warning: 'text-yellow-800 dark:text-yellow-200',
        info: 'text-blue-800 dark:text-blue-200'
    };

    const iconColors = {
        success: 'text-green-600 dark:text-green-400',
        error: 'text-red-600 dark:text-red-400',
        warning: 'text-yellow-600 dark:text-yellow-400',
        info: 'text-blue-600 dark:text-blue-400'
    };

    notification.classList.add(borderColors[type] || borderColors.info);

    notification.innerHTML = `
        <div class="flex items-center">
            <i class="${icon} ${iconColors[type] || iconColors.info} ml-3 text-xl"></i>
            <div class="flex-1">
                <p class="${textColors[type] || textColors.info} font-medium">${message}</p>
            </div>
            <button onclick="this.parentElement.parentElement.remove()" class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 ml-2">
                <i class="ri-close-line"></i>
            </button>
        </div>
    `;

    document.body.appendChild(notification);

    // Animate in
    setTimeout(() => {
        notification.classList.remove('-translate-x-full');
    }, 100);

    // Auto remove after 4 seconds
    setTimeout(() => {
        notification.classList.add('-translate-x-full');
        setTimeout(() => {
            if (notification.parentElement) {
                document.body.removeChild(notification);
            }
        }, 300);
    }, 4000);
}

function showSkeletonLoader(container, count = 3) {
    if (!container) return;

    const skeletonHTML = Array.from({length: count}, () => `
        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg overflow-hidden">
            <div class="skeleton h-64 w-full"></div>
            <div class="p-6">
                <div class="skeleton skeleton-text h-6 w-3/4 mb-2"></div>
                <div class="skeleton skeleton-text h-4 w-full mb-4"></div>
                <div class="flex items-center justify-between">
                    <div class="skeleton skeleton-text h-6 w-1/3"></div>
                    <div class="skeleton skeleton-button w-24"></div>
                </div>
            </div>
        </div>
    `).join('');

    container.innerHTML = skeletonHTML;
}

function addRippleEffect(element) {
    if (!element) return;

    element.classList.add('ripple');

    element.addEventListener('click', function(e) {
        const ripple = document.createElement('span');
        const rect = this.getBoundingClientRect();
        const size = Math.max(rect.width, rect.height);
        const x = e.clientX - rect.left - size / 2;
        const y = e.clientY - rect.top - size / 2;

        ripple.style.width = ripple.style.height = size + 'px';
        ripple.style.left = x + 'px';
        ripple.style.top = y + 'px';
        ripple.classList.add('ripple-effect');

        this.appendChild(ripple);

        setTimeout(() => {
            ripple.remove();
        }, 600);
    });
}

function smoothScrollTo(element, duration = 800) {
    if (!element) return;

    const targetPosition = element.offsetTop - 100;
    const startPosition = window.pageYOffset;
    const distance = targetPosition - startPosition;
    let startTime = null;

    function animation(currentTime) {
        if (startTime === null) startTime = currentTime;
        const timeElapsed = currentTime - startTime;
        const run = easeInOutQuad(timeElapsed, startPosition, distance, duration);
        window.scrollTo(0, run);
        if (timeElapsed < duration) requestAnimationFrame(animation);
    }

    function easeInOutQuad(t, b, c, d) {
        t /= d / 2;
        if (t < 1) return c / 2 * t * t + b;
        t--;
        return -c / 2 * (t * (t - 2) - 1) + b;
    }

    requestAnimationFrame(animation);
}

// Analytics System
let analyticsData = JSON.parse(localStorage.getItem('velasweets_analytics')) || {
    orders: [],
    productSales: {},
    totalRevenue: 0,
    activities: []
};

function openAnalyticsModal() {
    if (!analyticsModal) return;

    analyticsModal.classList.remove('hidden');
    analyticsModal.classList.add('scale-in');
    document.body.style.overflow = 'hidden';

    // Load analytics data
    loadAnalyticsData();

    setTimeout(() => {
        analyticsModal.classList.remove('scale-in');
    }, 300);
}

function closeAnalyticsModal() {
    if (!analyticsModal) return;

    analyticsModal.classList.add('hidden');
    document.body.style.overflow = 'auto';
}

function loadAnalyticsData() {
    // Calculate basic stats
    const totalOrders = analyticsData.orders.length;
    const totalRevenue = analyticsData.totalRevenue;
    const totalProducts = Object.keys(products).length;
    const averageRating = calculateOverallAverageRating();

    // Update stats cards
    document.getElementById('total-orders').textContent = totalOrders;
    document.getElementById('total-revenue').textContent = formatPrice(totalRevenue);
    document.getElementById('total-products').textContent = totalProducts;
    document.getElementById('average-rating').textContent = averageRating.toFixed(1);

    // Load best selling products
    loadBestSellingProducts();

    // Load recent activity
    loadRecentActivity();

    // Load product performance table
    loadProductPerformanceTable();
}

function calculateOverallAverageRating() {
    const allReviews = JSON.parse(localStorage.getItem('velasweets_reviews')) || [];
    if (allReviews.length === 0) return 0;

    const totalRating = allReviews.reduce((sum, review) => sum + review.rating, 0);
    return totalRating / allReviews.length;
}

function loadBestSellingProducts() {
    const bestSellingContainer = document.getElementById('best-selling-products');
    if (!bestSellingContainer) return;

    // Sort products by sales
    const productSalesArray = Object.entries(analyticsData.productSales)
        .map(([id, sales]) => ({
            id: parseInt(id),
            sales: sales,
            product: products[id]
        }))
        .filter(item => item.product)
        .sort((a, b) => b.sales - a.sales);

    if (productSalesArray.length === 0) {
        bestSellingContainer.innerHTML = `
            <div class="text-center py-8 text-gray-500 dark:text-gray-400">
                <i class="ri-bar-chart-line text-4xl mb-4"></i>
                <p>لا توجد بيانات مبيعات بعد</p>
            </div>
        `;
        return;
    }

    bestSellingContainer.innerHTML = productSalesArray.map((item, index) => {
        const percentage = productSalesArray.length > 0 ? (item.sales / productSalesArray[0].sales) * 100 : 0;

        return `
            <div class="flex items-center justify-between p-3 bg-white dark:bg-gray-600 rounded-lg">
                <div class="flex items-center gap-3">
                    <span class="w-6 h-6 bg-pink-600 text-white rounded-full flex items-center justify-center text-xs font-bold">
                        ${index + 1}
                    </span>
                    <div>
                        <p class="font-medium text-gray-900 dark:text-white">${item.product.name}</p>
                        <p class="text-sm text-gray-500 dark:text-gray-400">${item.sales} مبيعة</p>
                    </div>
                </div>
                <div class="text-right">
                    <div class="w-20 h-2 bg-gray-200 dark:bg-gray-500 rounded-full overflow-hidden">
                        <div class="h-full bg-pink-600 rounded-full" style="width: ${percentage}%"></div>
                    </div>
                </div>
            </div>
        `;
    }).join('');
}

function loadRecentActivity() {
    const recentActivityContainer = document.getElementById('recent-activity');
    if (!recentActivityContainer) return;

    const recentActivities = analyticsData.activities.slice(0, 10);

    if (recentActivities.length === 0) {
        recentActivityContainer.innerHTML = `
            <div class="text-center py-8 text-gray-500 dark:text-gray-400">
                <i class="ri-history-line text-4xl mb-4"></i>
                <p>لا يوجد نشاط حديث</p>
            </div>
        `;
        return;
    }

    recentActivityContainer.innerHTML = recentActivities.map(activity => {
        const timeAgo = getTimeAgo(new Date(activity.timestamp));
        const iconClass = getActivityIcon(activity.type);

        return `
            <div class="flex items-center gap-3 p-3 bg-white dark:bg-gray-600 rounded-lg">
                <i class="${iconClass} text-pink-600 dark:text-pink-400"></i>
                <div class="flex-1">
                    <p class="text-sm text-gray-900 dark:text-white">${activity.message}</p>
                    <p class="text-xs text-gray-500 dark:text-gray-400">${timeAgo}</p>
                </div>
            </div>
        `;
    }).join('');
}

function loadProductPerformanceTable() {
    const tableBody = document.getElementById('product-performance-table');
    if (!tableBody) return;

    const productPerformance = Object.keys(products).map(id => {
        const product = products[id];
        const sales = analyticsData.productSales[id] || 0;
        const revenue = sales * product.price;
        const reviews = getProductReviews(parseInt(id));
        const averageRating = reviews.length > 0 ?
            (reviews.reduce((sum, r) => sum + r.rating, 0) / reviews.length).toFixed(1) : '0.0';
        const favoritesCount = favorites.filter(f => f.id == id).length;

        return {
            id,
            product,
            sales,
            revenue,
            averageRating,
            favoritesCount,
            reviewsCount: reviews.length
        };
    });

    tableBody.innerHTML = productPerformance.map(item => `
        <tr class="border-b border-gray-200 dark:border-gray-600">
            <td class="py-3">
                <div class="flex items-center gap-3">
                    <img src="${item.product.image}" alt="${item.product.name}" class="w-10 h-10 object-cover rounded-lg">
                    <span class="font-medium text-gray-900 dark:text-white">${item.product.name}</span>
                </div>
            </td>
            <td class="py-3 text-gray-900 dark:text-white">${item.sales}</td>
            <td class="py-3 text-gray-900 dark:text-white">${formatPrice(item.revenue)}</td>
            <td class="py-3">
                <div class="flex items-center gap-1">
                    <span class="text-gray-900 dark:text-white">${item.averageRating}</span>
                    <i class="ri-star-fill text-yellow-400 text-sm"></i>
                    <span class="text-xs text-gray-500 dark:text-gray-400">(${item.reviewsCount})</span>
                </div>
            </td>
            <td class="py-3 text-gray-900 dark:text-white">${item.favoritesCount}</td>
        </tr>
    `).join('');
}

function getActivityIcon(type) {
    const icons = {
        order: 'ri-shopping-bag-line',
        favorite: 'ri-heart-line',
        review: 'ri-star-line',
        cart: 'ri-shopping-cart-line',
        coupon: 'ri-coupon-line'
    };
    return icons[type] || 'ri-information-line';
}

function addAnalyticsActivity(type, message) {
    const activity = {
        id: Date.now(),
        type: type,
        message: message,
        timestamp: new Date().toISOString()
    };

    analyticsData.activities.unshift(activity);

    // Keep only last 100 activities
    if (analyticsData.activities.length > 100) {
        analyticsData.activities = analyticsData.activities.slice(0, 100);
    }

    saveAnalyticsData();
}

function recordProductSale(productId, quantity = 1) {
    if (!analyticsData.productSales[productId]) {
        analyticsData.productSales[productId] = 0;
    }

    analyticsData.productSales[productId] += quantity;

    const product = products[productId];
    if (product) {
        analyticsData.totalRevenue += product.price * quantity;
        addAnalyticsActivity('order', `تم بيع ${quantity} من ${product.name}`);
    }

    saveAnalyticsData();
}

function saveAnalyticsData() {
    localStorage.setItem('velasweets_analytics', JSON.stringify(analyticsData));
}

function exportAnalyticsCSV() {
    const csvData = [
        ['المنتج', 'المبيعات', 'الإيرادات', 'التقييم', 'المفضلة'],
        ...Object.keys(products).map(id => {
            const product = products[id];
            const sales = analyticsData.productSales[id] || 0;
            const revenue = sales * product.price;
            const reviews = getProductReviews(parseInt(id));
            const averageRating = reviews.length > 0 ?
                (reviews.reduce((sum, r) => sum + r.rating, 0) / reviews.length).toFixed(1) : '0.0';
            const favoritesCount = favorites.filter(f => f.id == id).length;

            return [product.name, sales, revenue, averageRating, favoritesCount];
        })
    ];

    const csvContent = csvData.map(row => row.join(',')).join('\n');
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');

    if (link.download !== undefined) {
        const url = URL.createObjectURL(blob);
        link.setAttribute('href', url);
        link.setAttribute('download', 'velasweets-analytics.csv');
        link.style.visibility = 'hidden';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    }

    showEnhancedNotification('تم تصدير التقرير بنجاح', 'success', 'ri-download-line');
}

function printAnalyticsReport() {
    window.print();
}

// Security & Performance Enhancements
function sanitizeInput(input) {
    if (typeof input !== 'string') return input;

    return input
        .replace(/&/g, '&amp;')
        .replace(/</g, '&lt;')
        .replace(/>/g, '&gt;')
        .replace(/"/g, '&quot;')
        .replace(/'/g, '&#x27;')
        .replace(/\//g, '&#x2F;');
}

function validateEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

function validatePhoneNumber(phone) {
    const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/;
    return phoneRegex.test(phone.replace(/\s/g, ''));
}

function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

function throttle(func, limit) {
    let inThrottle;
    return function() {
        const args = arguments;
        const context = this;
        if (!inThrottle) {
            func.apply(context, args);
            inThrottle = true;
            setTimeout(() => inThrottle = false, limit);
        }
    };
}

// Lazy Loading for Images
function initializeLazyLoading() {
    if ('IntersectionObserver' in window) {
        const imageObserver = new IntersectionObserver((entries, observer) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const img = entry.target;
                    img.src = img.dataset.src;
                    img.classList.remove('lazy');
                    img.classList.add('loaded');
                    observer.unobserve(img);
                }
            });
        });

        document.querySelectorAll('img[data-src]').forEach(img => {
            imageObserver.observe(img);
        });
    } else {
        // Fallback for older browsers
        document.querySelectorAll('img[data-src]').forEach(img => {
            img.src = img.dataset.src;
            img.classList.remove('lazy');
            img.classList.add('loaded');
        });
    }
}

// Performance Monitoring
function measurePerformance(name, fn) {
    const start = performance.now();
    const result = fn();
    const end = performance.now();
    console.log(`${name} took ${end - start} milliseconds`);
    return result;
}

// Error Handling
function handleError(error, context = 'Unknown') {
    // Handle null or undefined errors
    if (!error) {
        console.warn(`Warning in ${context}: Error object is null or undefined`);
        return;
    }

    console.error(`Error in ${context}:`, error);

    // Log error for analytics (in production, send to error tracking service)
    const errorLog = {
        message: error.message || 'Unknown error',
        stack: error.stack || 'No stack trace available',
        context: context,
        timestamp: new Date().toISOString(),
        userAgent: navigator.userAgent,
        url: window.location.href
    };

    // Store error locally for debugging
    const errors = JSON.parse(localStorage.getItem('velasweets_errors')) || [];
    errors.unshift(errorLog);

    // Keep only last 50 errors
    if (errors.length > 50) {
        errors.splice(50);
    }

    localStorage.setItem('velasweets_errors', JSON.stringify(errors));

    // Show user-friendly error message
    showEnhancedNotification('حدث خطأ غير متوقع. يرجى المحاولة مرة أخرى.', 'error', 'ri-error-warning-line');
}

// Content Security Policy (CSP) helpers
function createSecureElement(tagName, attributes = {}) {
    const element = document.createElement(tagName);

    // Sanitize attributes
    Object.keys(attributes).forEach(key => {
        if (typeof attributes[key] === 'string') {
            element.setAttribute(key, sanitizeInput(attributes[key]));
        } else {
            element.setAttribute(key, attributes[key]);
        }
    });

    return element;
}

// Local Storage Security
const secureLocalStorage = {
    setItem: function(key, value) {
        try {
            const sanitizedKey = sanitizeInput(key);
            const serializedValue = JSON.stringify(value);
            localStorage.setItem(sanitizedKey, serializedValue);
        } catch (error) {
            handleError(error, 'secureLocalStorage.setItem');
        }
    },

    getItem: function(key) {
        try {
            const sanitizedKey = sanitizeInput(key);
            const item = localStorage.getItem(sanitizedKey);
            return item ? JSON.parse(item) : null;
        } catch (error) {
            handleError(error, 'secureLocalStorage.getItem');
            return null;
        }
    },

    removeItem: function(key) {
        try {
            const sanitizedKey = sanitizeInput(key);
            localStorage.removeItem(sanitizedKey);
        } catch (error) {
            handleError(error, 'secureLocalStorage.removeItem');
        }
    }
};

// Rate Limiting
const rateLimiter = {
    requests: new Map(),

    isAllowed: function(key, maxRequests = 10, windowMs = 60000) {
        const now = Date.now();
        const windowStart = now - windowMs;

        if (!this.requests.has(key)) {
            this.requests.set(key, []);
        }

        const requests = this.requests.get(key);

        // Remove old requests outside the window
        const validRequests = requests.filter(timestamp => timestamp > windowStart);
        this.requests.set(key, validRequests);

        if (validRequests.length >= maxRequests) {
            return false;
        }

        validRequests.push(now);
        return true;
    }
};

// Performance Optimization
function optimizePerformance() {
    // Preload critical resources
    const criticalResources = [
        'https://cdn.jsdelivr.net/npm/remixicon@3.5.0/fonts/remixicon.css',
        'https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap'
    ];

    criticalResources.forEach(resource => {
        const link = document.createElement('link');
        link.rel = 'preload';
        link.href = resource;
        link.as = 'style';
        document.head.appendChild(link);
    });

    // Optimize images
    document.querySelectorAll('img').forEach(img => {
        if (!img.loading) {
            img.loading = 'lazy';
        }

        if (!img.decoding) {
            img.decoding = 'async';
        }
    });

    // Enable passive event listeners for better scroll performance
    const passiveEvents = ['touchstart', 'touchmove', 'wheel'];
    passiveEvents.forEach(event => {
        document.addEventListener(event, () => {}, { passive: true });
    });
}

// Memory Management
function cleanupMemory() {
    // Clear old notifications
    const oldNotifications = document.querySelectorAll('.notification');
    oldNotifications.forEach(notification => {
        if (notification.parentElement) {
            notification.parentElement.removeChild(notification);
        }
    });

    // Clear old analytics data
    const analytics = JSON.parse(localStorage.getItem('velasweets_analytics')) || {};
    if (analytics.activities && analytics.activities.length > 100) {
        analytics.activities = analytics.activities.slice(0, 100);
        localStorage.setItem('velasweets_analytics', JSON.stringify(analytics));
    }

    // Clear old errors
    const errors = JSON.parse(localStorage.getItem('velasweets_errors')) || [];
    if (errors.length > 50) {
        const recentErrors = errors.slice(0, 50);
        localStorage.setItem('velasweets_errors', JSON.stringify(recentErrors));
    }
}

// Initialize security and performance enhancements
function initializeSecurityAndPerformance() {
    try {
        // Wrap all functions in try-catch for better error handling
        window.addEventListener('error', (event) => {
            const error = event.error || new Error(event.message || 'Unknown error');
            handleError(error, 'Global Error Handler');
        });

        window.addEventListener('unhandledrejection', (event) => {
            handleError(event.reason, 'Unhandled Promise Rejection');
        });

        // Initialize performance optimizations
        optimizePerformance();

        // Initialize lazy loading
        initializeLazyLoading();

        // Clean up memory periodically
        setInterval(cleanupMemory, 300000); // Every 5 minutes

        // Security headers applied silently

    } catch (error) {
        handleError(error, 'initializeSecurityAndPerformance');
    }
}

// Privacy Policy System
const privacyPolicyContent = {
    'privacy-policy': {
        title: 'سياسة الخصوصية',
        content: `
            <h2 class="text-xl font-bold mb-4">سياسة الخصوصية - VelaSweets</h2>
            <p class="mb-4">آخر تحديث: ${new Date().toLocaleDateString('ar-EG')}</p>

            <h3 class="text-lg font-semibold mb-3">1. المعلومات التي نجمعها</h3>
            <p class="mb-4">نحن في VelaSweets نحترم خصوصيتك ونلتزم بحماية معلوماتك الشخصية. نجمع المعلومات التالية:</p>
            <ul class="list-disc list-inside mb-4 space-y-2">
                <li>معلومات الاتصال (الاسم، البريد الإلكتروني، رقم الهاتف)</li>
                <li>معلومات الطلبات والمشتريات</li>
                <li>تفضيلات المنتجات والمفضلة</li>
                <li>معلومات تقنية (عنوان IP، نوع المتصفح)</li>
            </ul>

            <h3 class="text-lg font-semibold mb-3">2. كيف نستخدم معلوماتك</h3>
            <p class="mb-4">نستخدم معلوماتك للأغراض التالية:</p>
            <ul class="list-disc list-inside mb-4 space-y-2">
                <li>معالجة وتنفيذ طلباتك</li>
                <li>تحسين خدماتنا ومنتجاتنا</li>
                <li>إرسال إشعارات مهمة حول طلباتك</li>
                <li>تخصيص تجربة التسوق</li>
            </ul>

            <h3 class="text-lg font-semibold mb-3">3. حماية المعلومات</h3>
            <p class="mb-4">نتخذ إجراءات أمنية متقدمة لحماية معلوماتك:</p>
            <ul class="list-disc list-inside mb-4 space-y-2">
                <li>تشفير البيانات الحساسة</li>
                <li>استخدام بروتوكولات أمان متقدمة</li>
                <li>مراقبة مستمرة للأنشطة المشبوهة</li>
                <li>تحديث أنظمة الأمان بانتظام</li>
            </ul>

            <h3 class="text-lg font-semibold mb-3">4. مشاركة المعلومات</h3>
            <p class="mb-4">لا نبيع أو نؤجر معلوماتك الشخصية لأطراف ثالثة. قد نشارك معلوماتك فقط في الحالات التالية:</p>
            <ul class="list-disc list-inside mb-4 space-y-2">
                <li>مع مقدمي الخدمات الموثوقين لتنفيذ الطلبات</li>
                <li>عند الحاجة للامتثال للقوانين</li>
                <li>لحماية حقوقنا وسلامة المستخدمين</li>
            </ul>

            <h3 class="text-lg font-semibold mb-3">5. حقوقك</h3>
            <p class="mb-4">لديك الحقوق التالية فيما يتعلق بمعلوماتك:</p>
            <ul class="list-disc list-inside mb-4 space-y-2">
                <li>الوصول إلى معلوماتك الشخصية</li>
                <li>تصحيح المعلومات غير الدقيقة</li>
                <li>حذف معلوماتك (في ظروف معينة)</li>
                <li>إيقاف معالجة معلوماتك</li>
            </ul>

            <h3 class="text-lg font-semibold mb-3">6. الكوكيز</h3>
            <p class="mb-4">نستخدم الكوكيز لتحسين تجربتك على موقعنا. يمكنك التحكم في إعدادات الكوكيز من خلال متصفحك.</p>

            <h3 class="text-lg font-semibold mb-3">7. التواصل معنا</h3>
            <p class="mb-4">إذا كان لديك أي أسئلة حول سياسة الخصوصية، يمكنك التواصل معنا عبر:</p>
            <ul class="list-disc list-inside mb-4 space-y-2">
                <li>البريد الإلكتروني: <EMAIL></li>
                <li>الهاتف: +964 123 456 789</li>
                <li>العنوان: بغداد، العراق</li>
            </ul>
        `
    },
    'terms-of-service': {
        title: 'شروط الاستخدام',
        content: `
            <h2 class="text-xl font-bold mb-4">شروط الاستخدام - VelaSweets</h2>
            <p class="mb-4">آخر تحديث: ${new Date().toLocaleDateString('ar-EG')}</p>

            <h3 class="text-lg font-semibold mb-3">1. قبول الشروط</h3>
            <p class="mb-4">باستخدام موقع VelaSweets، فإنك توافق على هذه الشروط والأحكام. إذا كنت لا توافق على أي من هذه الشروط، يرجى عدم استخدام الموقع.</p>

            <h3 class="text-lg font-semibold mb-3">2. استخدام الموقع</h3>
            <p class="mb-4">يمكنك استخدام موقعنا للأغراض التالية:</p>
            <ul class="list-disc list-inside mb-4 space-y-2">
                <li>تصفح وشراء المنتجات</li>
                <li>إنشاء حساب شخصي</li>
                <li>كتابة مراجعات للمنتجات</li>
                <li>التواصل مع خدمة العملاء</li>
            </ul>

            <h3 class="text-lg font-semibold mb-3">3. الطلبات والدفع</h3>
            <ul class="list-disc list-inside mb-4 space-y-2">
                <li>جميع الأسعار بالدينار العراقي</li>
                <li>نحتفظ بالحق في تغيير الأسعار دون إشعار مسبق</li>
                <li>الدفع مطلوب عند تأكيد الطلب</li>
                <li>نقبل الدفع نقداً عند التسليم</li>
            </ul>

            <h3 class="text-lg font-semibold mb-3">4. التسليم</h3>
            <ul class="list-disc list-inside mb-4 space-y-2">
                <li>نسعى للتسليم في الوقت المحدد</li>
                <li>قد تختلف أوقات التسليم حسب الموقع</li>
                <li>المنتجات الطازجة لها مدة صلاحية محدودة</li>
            </ul>

            <h3 class="text-lg font-semibold mb-3">5. الإرجاع والاستبدال</h3>
            <ul class="list-disc list-inside mb-4 space-y-2">
                <li>يمكن إرجاع المنتجات في حالة وجود عيب</li>
                <li>يجب الإبلاغ عن المشاكل خلال 24 ساعة</li>
                <li>المنتجات المستهلكة لا يمكن إرجاعها</li>
            </ul>

            <h3 class="text-lg font-semibold mb-3">6. المسؤولية</h3>
            <p class="mb-4">نحن غير مسؤولين عن:</p>
            <ul class="list-disc list-inside mb-4 space-y-2">
                <li>الأضرار الناتجة عن سوء الاستخدام</li>
                <li>التأخير بسبب ظروف خارجة عن إرادتنا</li>
                <li>الحساسية من المكونات</li>
            </ul>
        `
    },
    'cookie-policy': {
        title: 'سياسة الكوكيز',
        content: `
            <h2 class="text-xl font-bold mb-4">سياسة الكوكيز - VelaSweets</h2>
            <p class="mb-4">آخر تحديث: ${new Date().toLocaleDateString('ar-EG')}</p>

            <h3 class="text-lg font-semibold mb-3">ما هي الكوكيز؟</h3>
            <p class="mb-4">الكوكيز هي ملفات نصية صغيرة يتم حفظها على جهازك عند زيارة موقعنا. تساعدنا في تحسين تجربتك وتذكر تفضيلاتك.</p>

            <h3 class="text-lg font-semibold mb-3">أنواع الكوكيز التي نستخدمها</h3>

            <h4 class="font-semibold mb-2">1. الكوكيز الأساسية</h4>
            <p class="mb-4">ضرورية لعمل الموقع بشكل صحيح:</p>
            <ul class="list-disc list-inside mb-4 space-y-2">
                <li>حفظ محتويات السلة</li>
                <li>تذكر تسجيل الدخول</li>
                <li>إعدادات اللغة والثيم</li>
            </ul>

            <h4 class="font-semibold mb-2">2. كوكيز الأداء</h4>
            <p class="mb-4">تساعدنا في فهم كيفية استخدام الموقع:</p>
            <ul class="list-disc list-inside mb-4 space-y-2">
                <li>عدد الزوار</li>
                <li>الصفحات الأكثر زيارة</li>
                <li>مدة البقاء في الموقع</li>
            </ul>

            <h4 class="font-semibold mb-2">3. كوكيز التخصيص</h4>
            <p class="mb-4">لتخصيص تجربتك:</p>
            <ul class="list-disc list-inside mb-4 space-y-2">
                <li>المنتجات المفضلة</li>
                <li>التفضيلات الشخصية</li>
                <li>المنتجات المشاهدة مؤخراً</li>
            </ul>

            <h3 class="text-lg font-semibold mb-3">إدارة الكوكيز</h3>
            <p class="mb-4">يمكنك التحكم في الكوكيز من خلال:</p>
            <ul class="list-disc list-inside mb-4 space-y-2">
                <li>إعدادات المتصفح</li>
                <li>حذف الكوكيز الموجودة</li>
                <li>منع حفظ كوكيز جديدة</li>
            </ul>

            <p class="mb-4"><strong>ملاحظة:</strong> إيقاف الكوكيز قد يؤثر على وظائف الموقع.</p>
        `
    }
};

function openPrivacyModal(type = 'privacy-policy') {
    if (!privacyModal) return;

    const content = privacyPolicyContent[type];
    if (!content) return;

    // Update modal content
    document.getElementById('privacy-modal-title').textContent = content.title;
    document.getElementById('privacy-modal-content').innerHTML = content.content;

    // Show modal
    privacyModal.classList.remove('hidden');
    privacyModal.classList.add('scale-in');
    document.body.style.overflow = 'hidden';

    setTimeout(() => {
        privacyModal.classList.remove('scale-in');
    }, 300);
}

function closePrivacyModal() {
    if (!privacyModal) return;

    privacyModal.classList.add('hidden');
    document.body.style.overflow = 'auto';
}

// Profile Dropdown Functions
function toggleProfileDropdown() {
    const profileDropdown = document.getElementById('profile-dropdown');
    if (!profileDropdown) return;

    const isHidden = profileDropdown.classList.contains('hidden');

    if (isHidden) {
        profileDropdown.classList.remove('hidden');
        profileDropdown.classList.add('scale-in');
    } else {
        closeProfileDropdown();
    }
}

function closeProfileDropdown() {
    const profileDropdown = document.getElementById('profile-dropdown');
    if (!profileDropdown) return;

    profileDropdown.classList.add('hidden');
    profileDropdown.classList.remove('scale-in');
}

// Mobile Profile Modal Functions
function openMobileProfileModal() {
    if (!mobileProfileModal) return;

    mobileProfileModal.classList.remove('hidden');
    mobileProfileModal.classList.add('slide-in-up');
    document.body.style.overflow = 'hidden';

    setTimeout(() => {
        mobileProfileModal.classList.remove('slide-in-up');
    }, 300);
}

function closeMobileProfileModal() {
    if (!mobileProfileModal) return;

    mobileProfileModal.classList.add('hidden');
    document.body.style.overflow = 'auto';
}

// Language System
function toggleLanguageDropdown() {
    const languageDropdown = document.getElementById('language-dropdown');
    if (!languageDropdown) return;

    const isHidden = languageDropdown.classList.contains('hidden');

    if (isHidden) {
        languageDropdown.classList.remove('hidden');
        languageDropdown.classList.add('scale-in');
    } else {
        closeLanguageDropdown();
    }
}

function closeLanguageDropdown() {
    const languageDropdown = document.getElementById('language-dropdown');
    if (!languageDropdown) return;

    languageDropdown.classList.add('hidden');
    languageDropdown.classList.remove('scale-in');
}

function changeLanguage(lang) {
    currentLanguage = lang;
    localStorage.setItem('velasweets_language', lang);

    // Update active language indicator
    document.querySelectorAll('.language-option').forEach(option => {
        const checkIcon = option.querySelector('.ri-check-line');
        if (option.dataset.lang === lang) {
            option.classList.add('active');
            if (checkIcon) checkIcon.style.display = 'block';
        } else {
            option.classList.remove('active');
            if (checkIcon) checkIcon.style.display = 'none';
        }
    });

    // Close dropdown
    closeLanguageDropdown();

    // Show notification
    const languageNames = {
        'ar': 'العربية',
        'en': 'English',
        'ku': 'کوردی'
    };

    showEnhancedNotification(`تم تغيير اللغة إلى ${languageNames[lang]}`, 'success', 'ri-global-line');

    // Here you would typically reload content in the new language
    // For now, we'll just show a message that this feature is coming soon
    if (lang !== 'ar') {
        setTimeout(() => {
            showEnhancedNotification('ميزة تغيير اللغة قيد التطوير - سيتم إضافتها قريباً', 'info', 'ri-information-line');
        }, 1500);
    }
}

// Helper functions for checkout profile management
function editProfileFromCheckout() {
    // Close checkout modal
    closeCheckoutModal();

    // Open profile modal
    openProfileModal();

    // Edit current profile
    if (currentProfileId) {
        editProfile(currentProfileId);
    }

    showEnhancedNotification('يمكنك تعديل بياناتك من هنا', 'info', 'ri-user-line');
}

function createProfileFromCheckout() {
    // Close checkout modal
    closeCheckoutModal();

    // Open profile modal
    openProfileModal();

    // Clear form for new profile
    clearProfileForm();

    showEnhancedNotification('أنشئ ملفك الشخصي لإتمام الطلب', 'info', 'ri-user-add-line');
}

// Profile Management System
function openProfileModal() {
    if (!profileModal) return;

    profileModal.classList.remove('hidden');
    profileModal.classList.add('scale-in');
    document.body.style.overflow = 'hidden';

    loadProfilesList();

    setTimeout(() => {
        profileModal.classList.remove('scale-in');
    }, 300);
}

function closeProfileModal() {
    if (!profileModal) return;

    profileModal.classList.add('hidden');
    document.body.style.overflow = 'auto';
    clearProfileForm();
}

function loadProfilesList() {
    const profilesList = document.getElementById('profiles-list');
    if (!profilesList) return;

    if (profiles.length === 0) {
        profilesList.innerHTML = `
            <div class="text-center py-8 text-gray-500 dark:text-gray-400">
                <i class="ri-user-line text-4xl mb-4"></i>
                <p>لا توجد ملفات شخصية. أضف ملفك الأول!</p>
            </div>
        `;
        return;
    }

    profilesList.innerHTML = profiles.map(profile => `
        <div class="profile-item p-4 border border-gray-200 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors ${currentProfileId == profile.id ? 'ring-2 ring-pink-500 bg-pink-50 dark:bg-pink-900/20' : ''}" data-id="${profile.id}">
            <div class="flex items-center justify-between">
                <div class="flex-1">
                    <div class="flex items-center gap-2 mb-1">
                        <h5 class="font-semibold text-gray-900 dark:text-white">${profile.name}</h5>
                        <span class="text-xs bg-gray-100 dark:bg-gray-600 text-gray-600 dark:text-gray-300 px-2 py-1 rounded">
                            ${getProfileTypeLabel(profile.type)}
                        </span>
                        ${currentProfileId == profile.id ? '<span class="text-xs bg-pink-100 dark:bg-pink-900 text-pink-600 dark:text-pink-300 px-2 py-1 rounded">افتراضي</span>' : ''}
                    </div>
                    <p class="text-sm text-gray-600 dark:text-gray-400">${profile.governorate} - ${profile.phone1}</p>
                    <p class="text-xs text-gray-500 dark:text-gray-500 mt-1">${profile.address.substring(0, 50)}${profile.address.length > 50 ? '...' : ''}</p>
                </div>
                <div class="flex items-center gap-2">
                    <button onclick="editProfile(${profile.id})" class="p-2 text-blue-600 hover:bg-blue-100 dark:hover:bg-blue-900/20 rounded transition-colors" title="تعديل">
                        <i class="ri-edit-line"></i>
                    </button>
                    <button onclick="setDefaultProfile(${profile.id})" class="p-2 text-green-600 hover:bg-green-100 dark:hover:bg-green-900/20 rounded transition-colors" title="تعيين كافتراضي">
                        <i class="ri-star-line"></i>
                    </button>
                    <button onclick="deleteProfile(${profile.id})" class="p-2 text-red-600 hover:bg-red-100 dark:hover:bg-red-900/20 rounded transition-colors" title="حذف">
                        <i class="ri-delete-bin-line"></i>
                    </button>
                </div>
            </div>
        </div>
    `).join('');
}

function getProfileTypeLabel(type) {
    const labels = {
        'home': 'المنزل',
        'work': 'العمل',
        'other': 'أخرى'
    };
    return labels[type] || 'أخرى';
}

function clearProfileForm() {
    document.getElementById('profile-id').value = '';
    document.getElementById('profile-name').value = '';
    document.getElementById('profile-type').value = 'home';
    document.getElementById('profile-phone1').value = '';
    document.getElementById('profile-phone2').value = '';
    document.getElementById('profile-governorate').value = '';
    document.getElementById('profile-address').value = '';
    document.getElementById('delete-profile-btn').classList.add('hidden');
}

function editProfile(profileId) {
    const profile = profiles.find(p => p.id === profileId);
    if (!profile) return;

    document.getElementById('profile-id').value = profile.id;
    document.getElementById('profile-name').value = profile.name;
    document.getElementById('profile-type').value = profile.type;
    document.getElementById('profile-phone1').value = profile.phone1;
    document.getElementById('profile-phone2').value = profile.phone2 || '';
    document.getElementById('profile-governorate').value = profile.governorate;
    document.getElementById('profile-address').value = profile.address;
    document.getElementById('delete-profile-btn').classList.remove('hidden');
}

function setDefaultProfile(profileId) {
    currentProfileId = profileId;
    localStorage.setItem('velasweets_current_profile', profileId);
    loadProfilesList();

    // Update checkout if open
    if (!checkoutModal.classList.contains('hidden')) {
        setTimeout(() => {
            populateCheckoutItems();
        }, 100);
    }

    showEnhancedNotification('تم تعيين الملف الشخصي كافتراضي', 'success', 'ri-star-fill');
}

function deleteProfile(profileId) {
    if (profiles.length === 1) {
        showEnhancedNotification('لا يمكن حذف الملف الوحيد', 'error', 'ri-error-warning-line');
        return;
    }

    if (confirm('هل أنت متأكد من حذف هذا الملف الشخصي؟')) {
        profiles = profiles.filter(p => p.id !== profileId);

        if (currentProfileId == profileId) {
            currentProfileId = profiles[0]?.id || null;
            localStorage.setItem('velasweets_current_profile', currentProfileId);
        }

        localStorage.setItem('velasweets_profiles', JSON.stringify(profiles));
        loadProfilesList();
        clearProfileForm();
        showEnhancedNotification('تم حذف الملف الشخصي', 'success', 'ri-delete-bin-line');
    }
}

function validatePhoneNumber(phone) {
    const phoneRegex = /^07[0-9]{9}$/;
    return phoneRegex.test(phone);
}

function handleProfileSubmit(e) {
    e.preventDefault();

    try {
        const profileId = document.getElementById('profile-id').value;
        const name = sanitizeInput(document.getElementById('profile-name').value.trim());
        const type = document.getElementById('profile-type').value;
        const phone1 = document.getElementById('profile-phone1').value.trim();
        const phone2 = document.getElementById('profile-phone2').value.trim();
        const governorate = document.getElementById('profile-governorate').value;
        const address = sanitizeInput(document.getElementById('profile-address').value.trim());

        // Validation
        if (!name) {
            showEnhancedNotification('يرجى إدخال الاسم الكامل', 'error', 'ri-user-line');
            return;
        }

        if (!validatePhoneNumber(phone1)) {
            showEnhancedNotification('رقم الهاتف الأساسي غير صحيح (يجب أن يبدأ بـ 07 ويحتوي على 11 رقم)', 'error', 'ri-phone-line');
            return;
        }

        if (phone2 && !validatePhoneNumber(phone2)) {
            showEnhancedNotification('رقم الهاتف الاحتياطي غير صحيح', 'error', 'ri-phone-line');
            return;
        }

        if (!governorate) {
            showEnhancedNotification('يرجى اختيار المحافظة', 'error', 'ri-map-pin-line');
            return;
        }

        if (!address || address.length < 10) {
            showEnhancedNotification('يرجى إدخال عنوان تفصيلي (10 أحرف على الأقل)', 'error', 'ri-map-pin-line');
            return;
        }

        const profileData = {
            id: profileId || Date.now(),
            name: name,
            type: type,
            phone1: phone1,
            phone2: phone2,
            governorate: governorate,
            address: address,
            createdAt: profileId ? profiles.find(p => p.id == profileId)?.createdAt : new Date().toISOString(),
            updatedAt: new Date().toISOString()
        };

        if (profileId) {
            // Update existing profile
            const index = profiles.findIndex(p => p.id == profileId);
            if (index !== -1) {
                profiles[index] = profileData;
            }
            showEnhancedNotification('تم تحديث الملف الشخصي', 'success', 'ri-check-line');
        } else {
            // Add new profile
            profiles.push(profileData);

            // Set as default if it's the first profile
            if (profiles.length === 1) {
                currentProfileId = profileData.id;
                localStorage.setItem('velasweets_current_profile', currentProfileId);
            }

            showEnhancedNotification('تم إضافة الملف الشخصي', 'success', 'ri-user-add-line');
        }

        localStorage.setItem('velasweets_profiles', JSON.stringify(profiles));
        loadProfilesList();
        clearProfileForm();

        // If checkout modal was open, refresh it
        if (!checkoutModal.classList.contains('hidden')) {
            setTimeout(() => {
                populateCheckoutItems();
            }, 500);
        }

    } catch (error) {
        handleError(error, 'handleProfileSubmit');
    }
}

// Orders Tracking System
function openOrdersModal() {
    if (!ordersModal) return;

    ordersModal.classList.remove('hidden');
    ordersModal.classList.add('scale-in');
    document.body.style.overflow = 'hidden';

    loadOrdersList();

    setTimeout(() => {
        ordersModal.classList.remove('scale-in');
    }, 300);
}

function closeOrdersModal() {
    if (!ordersModal) return;

    ordersModal.classList.add('hidden');
    document.body.style.overflow = 'auto';
}

function loadOrdersList() {
    try {
        const ordersList = document.getElementById('orders-list');
        const noOrders = document.getElementById('no-orders');

        if (!ordersList || !noOrders) return;

        const orders = JSON.parse(localStorage.getItem('velasweets_orders')) || [];

        // Validate orders data
        const validOrders = orders.filter(order => {
            return order &&
                   typeof order === 'object' &&
                   order.id &&
                   order.items &&
                   Array.isArray(order.items) &&
                   order.total !== undefined;
        });

    if (orders.length === 0) {
            ordersList.classList.add('hidden');
            noOrders.classList.remove('hidden');
            return;
        }

        ordersList.classList.remove('hidden');
        noOrders.classList.add('hidden');

        ordersList.innerHTML = validOrders.map(order => {
        const statusInfo = getOrderStatusInfo(order.status);
        const canCancel = order.status === 'pending';
        const canReorder = order.status === 'cancelled' && !order.reordered;

        return `
            <div class="order-item bg-gray-50 dark:bg-gray-700 rounded-xl p-6">
                <!-- Order Header -->
                <div class="flex items-center justify-between mb-4">
                    <div>
                        <h4 class="text-lg font-semibold text-gray-900 dark:text-white">طلب #${order.id}</h4>
                        <p class="text-sm text-gray-500 dark:text-gray-400">${new Date(order.timestamp).toLocaleDateString('ar-EG', {
                            year: 'numeric',
                            month: 'long',
                            day: 'numeric',
                            hour: '2-digit',
                            minute: '2-digit'
                        })}</p>
                    </div>
                    <div class="text-right">
                        <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${statusInfo.bgColor} ${statusInfo.textColor}">
                            <i class="${statusInfo.icon} ml-1"></i>
                            ${statusInfo.label}
                        </span>
                        <p class="text-lg font-bold text-gray-900 dark:text-white mt-1">${formatPrice(order.total)}</p>
                    </div>
                </div>

                <!-- Order Timeline -->
                <div class="mb-4">
                    <div class="flex items-center justify-between text-sm">
                        ${getOrderTimeline(order.status)}
                    </div>
                </div>

                <!-- Order Items -->
                <div class="mb-4">
                    <h5 class="font-medium text-gray-900 dark:text-white mb-2">تفاصيل الطلب:</h5>
                    <div class="space-y-2">
                        ${order.items.map(item => `
                            <div class="flex items-center justify-between text-sm">
                                <span class="text-gray-600 dark:text-gray-400">${item.name} × ${item.quantity}</span>
                                <span class="text-gray-900 dark:text-white">${formatPrice(item.price * item.quantity)}</span>
                            </div>
                        `).join('')}
                        ${order.subtotal ? `
                            <div class="border-t border-gray-200 dark:border-gray-600 pt-2 mt-2">
                                <div class="flex items-center justify-between text-sm">
                                    <span class="text-gray-600 dark:text-gray-400">مجموع المنتجات:</span>
                                    <span class="text-gray-900 dark:text-white">${formatPrice(order.subtotal)}</span>
                                </div>
                                <div class="flex items-center justify-between text-sm">
                                    <span class="text-gray-600 dark:text-gray-400">رسوم التوصيل:</span>
                                    <span class="text-gray-900 dark:text-white">${formatPrice(order.deliveryFee || 0)}</span>
                                </div>
                                <div class="flex items-center justify-between text-sm font-semibold border-t border-gray-200 dark:border-gray-600 pt-1 mt-1">
                                    <span class="text-gray-900 dark:text-white">المجموع النهائي:</span>
                                    <span class="text-pink-600 dark:text-pink-400">${formatPrice(order.total)}</span>
                                </div>
                            </div>
                        ` : ''}
                    </div>
                </div>

                <!-- Customer Info -->
                <div class="mb-4 text-sm">
                    <p class="text-gray-600 dark:text-gray-400"><strong>العميل:</strong> ${order.customer?.name || 'غير محدد'}</p>
                    <p class="text-gray-600 dark:text-gray-400"><strong>الهاتف:</strong> ${order.customer?.phone || 'غير محدد'}</p>
                    <p class="text-gray-600 dark:text-gray-400"><strong>العنوان:</strong> ${order.customer?.address || 'غير محدد'}</p>
                    ${order.notes ? `<p class="text-gray-600 dark:text-gray-400"><strong>ملاحظات:</strong> ${order.notes}</p>` : ''}
                </div>

                <!-- Order Actions -->
                <div class="flex gap-2">
                    ${canCancel ? `
                        <button onclick="cancelOrder('${order.id}')" class="px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg font-medium transition-colors flex items-center gap-2">
                            <i class="ri-close-line"></i>
                            إلغاء الطلب
                        </button>
                    ` : ''}
                    ${canReorder ? `
                        <button onclick="reorderOrder('${order.id}')" class="px-4 py-2 bg-pink-600 hover:bg-pink-700 text-white rounded-lg font-medium transition-colors flex items-center gap-2">
                            <i class="ri-refresh-line"></i>
                            إعادة الطلب
                        </button>
                    ` : ''}
                </div>
            </div>
        `;
    }).join('');

    } catch (error) {
        handleError(error, 'loadOrdersList');
        // Show error message to user
        if (ordersList) {
            ordersList.innerHTML = `
                <div class="text-center py-8 text-red-600 dark:text-red-400">
                    <i class="ri-error-warning-line text-4xl mb-4"></i>
                    <p>حدث خطأ في تحميل الطلبات</p>
                </div>
            `;
        }
    }
}

function getOrderStatusInfo(status) {
    const statusMap = {
        'pending': {
            label: 'قيد المراجعة',
            icon: 'ri-time-line',
            bgColor: 'bg-yellow-100 dark:bg-yellow-900/20',
            textColor: 'text-yellow-800 dark:text-yellow-300'
        },
        'confirmed': {
            label: 'تم القبول',
            icon: 'ri-check-line',
            bgColor: 'bg-blue-100 dark:bg-blue-900/20',
            textColor: 'text-blue-800 dark:text-blue-300'
        },
        'preparing': {
            label: 'قيد التجهيز',
            icon: 'ri-tools-line',
            bgColor: 'bg-purple-100 dark:bg-purple-900/20',
            textColor: 'text-purple-800 dark:text-purple-300'
        },
        'delivering': {
            label: 'قيد التوصيل',
            icon: 'ri-truck-line',
            bgColor: 'bg-orange-100 dark:bg-orange-900/20',
            textColor: 'text-orange-800 dark:text-orange-300'
        },
        'completed': {
            label: 'تم الاكتمال',
            icon: 'ri-check-double-line',
            bgColor: 'bg-green-100 dark:bg-green-900/20',
            textColor: 'text-green-800 dark:text-green-300'
        },
        'cancelled': {
            label: 'تم الإلغاء',
            icon: 'ri-close-line',
            bgColor: 'bg-red-100 dark:bg-red-900/20',
            textColor: 'text-red-800 dark:text-red-300'
        }
    };

    return statusMap[status] || statusMap['pending'];
}

function getOrderTimeline(currentStatus) {
    const steps = [
        { key: 'pending', label: 'قيد المراجعة' },
        { key: 'confirmed', label: 'تم القبول' },
        { key: 'preparing', label: 'قيد التجهيز' },
        { key: 'delivering', label: 'قيد التوصيل' },
        { key: 'completed', label: 'تم الاكتمال' }
    ];

    const currentIndex = steps.findIndex(step => step.key === currentStatus);
    const isCancelled = currentStatus === 'cancelled';

    if (isCancelled) {
        return `<span class="text-red-600 dark:text-red-400">تم إلغاء الطلب</span>`;
    }

    return steps.map((step, index) => {
        const isActive = index <= currentIndex;
        const isCurrent = index === currentIndex;

        return `
            <div class="flex items-center ${index < steps.length - 1 ? 'flex-1' : ''}">
                <div class="flex items-center">
                    <div class="w-3 h-3 rounded-full ${isActive ? 'bg-pink-600' : 'bg-gray-300 dark:bg-gray-600'} ${isCurrent ? 'ring-4 ring-pink-200 dark:ring-pink-800' : ''}"></div>
                    <span class="mr-2 text-xs ${isActive ? 'text-pink-600 dark:text-pink-400 font-medium' : 'text-gray-500 dark:text-gray-400'}">${step.label}</span>
                </div>
                ${index < steps.length - 1 ? `<div class="flex-1 h-px ${isActive ? 'bg-pink-600' : 'bg-gray-300 dark:bg-gray-600'} mx-2"></div>` : ''}
            </div>
        `;
    }).join('');
}

function cancelOrder(orderId) {
    if (!confirm('هل أنت متأكد من إلغاء هذا الطلب؟')) return;

    const orders = JSON.parse(localStorage.getItem('velasweets_orders')) || [];
    const orderIndex = orders.findIndex(order => order.id === orderId);

    if (orderIndex !== -1) {
        orders[orderIndex].status = 'cancelled';
        orders[orderIndex].cancelledAt = new Date().toISOString();

        localStorage.setItem('velasweets_orders', JSON.stringify(orders));
        loadOrdersList();

        showEnhancedNotification('تم إلغاء الطلب بنجاح', 'success', 'ri-check-line');
        addNotification(`تم إلغاء الطلب #${orderId}`, 'info', 'orders');
    }
}

function reorderOrder(orderId) {
    const orders = JSON.parse(localStorage.getItem('velasweets_orders')) || [];
    const order = orders.find(o => o.id === orderId);

    if (!order) return;

    // Add items back to cart
    order.items.forEach(item => {
        const existingItem = cart.find(cartItem => cartItem.id === item.id);
        if (existingItem) {
            existingItem.quantity += item.quantity;
        } else {
            cart.push({...item});
        }
    });

    // Mark order as reordered
    order.reordered = true;
    localStorage.setItem('velasweets_orders', JSON.stringify(orders));

    // Save cart
    localStorage.setItem('velasweets_cart', JSON.stringify(cart));
    updateCartUI();

    // Close modal and show cart
    closeOrdersModal();
    openCart();

    showEnhancedNotification('تم إضافة المنتجات إلى السلة', 'success', 'ri-shopping-cart-line');
    loadOrdersList();
}

// Checkout System
function openCheckoutModal() {
    if (!checkoutModal || cart.length === 0) return;

    // Clean up any previous messages
    const existingMessages = checkoutModal.querySelectorAll('.bg-yellow-50, .bg-yellow-900\\/20');
    existingMessages.forEach(msg => msg.remove());

    // Reset submit button
    const submitButton = checkoutModal.querySelector('button[type="submit"]');
    if (submitButton) {
        submitButton.disabled = false;
        submitButton.classList.remove('opacity-50', 'cursor-not-allowed');
    }

    // Populate checkout items
    populateCheckoutItems();

    // Show modal
    checkoutModal.classList.remove('hidden');
    checkoutModal.classList.add('scale-in');
    document.body.style.overflow = 'hidden';

    setTimeout(() => {
        checkoutModal.classList.remove('scale-in');
    }, 300);
}

function closeCheckoutModal() {
    if (!checkoutModal) return;

    checkoutModal.classList.add('hidden');
    document.body.style.overflow = 'auto';
}

function populateCheckoutItems() {
    const checkoutItemsContainer = document.getElementById('checkout-items');
    const checkoutTotalElement = document.getElementById('checkout-total');
    const checkoutSubtotalElement = document.getElementById('checkout-subtotal');

    if (!checkoutItemsContainer || !checkoutTotalElement) return;

    // Calculate subtotal
    const subtotal = cart.reduce((sum, item) => sum + (item.price * item.quantity), 0);

    // Populate items
    checkoutItemsContainer.innerHTML = cart.map(item => `
        <div class="flex items-center justify-between py-2">
            <div class="flex items-center gap-3">
                <img src="${item.image || 'https://images.unsplash.com/photo-1578985545062-69928b1d9587?w=100&h=100&fit=crop&crop=center'}" alt="${item.name}" class="w-12 h-12 object-cover rounded-lg" onerror="this.src='https://images.unsplash.com/photo-1578985545062-69928b1d9587?w=100&h=100&fit=crop&crop=center'">
                <div>
                    <p class="font-medium text-gray-900 dark:text-white">${item.name}</p>
                    <p class="text-sm text-gray-500 dark:text-gray-400">الكمية: ${item.quantity}</p>
                </div>
            </div>
            <p class="font-semibold text-gray-900 dark:text-white">${formatPrice(item.price * item.quantity)}</p>
        </div>
    `).join('');

    // Update subtotal
    if (checkoutSubtotalElement) {
        checkoutSubtotalElement.textContent = formatPrice(subtotal);
    }

    // Calculate delivery fee and total
    updateDeliveryFeeAndTotal(subtotal);

    // Pre-fill with default profile if available (read-only)
    const defaultProfile = profiles.find(p => p.id == currentProfileId);
    const customerNameField = document.getElementById('customer-name');
    const customerPhoneField = document.getElementById('customer-phone');
    const customerAddressField = document.getElementById('customer-address');

    if (defaultProfile && customerNameField && customerPhoneField && customerAddressField) {
        // Fill the fields
        customerNameField.value = defaultProfile.name;
        customerPhoneField.value = defaultProfile.phone1;
        customerAddressField.value = `${defaultProfile.governorate} - ${defaultProfile.address}`;

        // Make fields read-only
        customerNameField.readOnly = true;
        customerPhoneField.readOnly = true;
        customerAddressField.readOnly = true;

        // Add styling for read-only fields
        [customerNameField, customerPhoneField, customerAddressField].forEach(field => {
            field.classList.add('bg-gray-100', 'dark:bg-gray-700', 'cursor-not-allowed');
            field.classList.remove('bg-white', 'dark:bg-gray-800');
        });

        // Add edit button if not exists
        if (!document.getElementById('edit-profile-link')) {
            const editButton = document.createElement('div');
            editButton.id = 'edit-profile-link';
            editButton.className = 'mt-3 text-center';
            editButton.innerHTML = `
                <button type="button" onclick="editProfileFromCheckout()" class="text-pink-600 dark:text-pink-400 hover:text-pink-700 dark:hover:text-pink-300 text-sm font-medium flex items-center gap-2 mx-auto">
                    <i class="ri-edit-line"></i>
                    تعديل البيانات الشخصية
                </button>
            `;
            customerAddressField.parentNode.appendChild(editButton);
        }
    } else if (!defaultProfile) {
        // Show message to create profile first
        const noProfileMessage = document.createElement('div');
        noProfileMessage.className = 'bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4 mb-4';
        noProfileMessage.innerHTML = `
            <div class="flex items-center gap-3">
                <i class="ri-information-line text-yellow-600 dark:text-yellow-400 text-xl"></i>
                <div>
                    <p class="text-yellow-800 dark:text-yellow-200 font-medium">يرجى إنشاء ملف شخصي أولاً</p>
                    <p class="text-yellow-700 dark:text-yellow-300 text-sm mt-1">لإتمام الطلب، يجب إنشاء ملف شخصي يحتوي على بياناتك</p>
                    <button type="button" onclick="createProfileFromCheckout()" class="mt-2 bg-yellow-600 hover:bg-yellow-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors">
                        إنشاء ملف شخصي
                    </button>
                </div>
            </div>
        `;

        const checkoutForm = document.getElementById('checkout-form');
        if (checkoutForm) {
            checkoutForm.insertBefore(noProfileMessage, checkoutForm.firstChild);

            // Disable submit button
            const submitButton = checkoutForm.querySelector('button[type="submit"]');
            if (submitButton) {
                submitButton.disabled = true;
                submitButton.classList.add('opacity-50', 'cursor-not-allowed');
            }
        }
    }
}

// Payment method handling removed - Cash on delivery only

function updateDeliveryFeeAndTotal(subtotal) {
    const checkoutTotalElement = document.getElementById('checkout-total');
    const checkoutDeliveryElement = document.getElementById('checkout-delivery');

    if (!checkoutTotalElement || !checkoutDeliveryElement) return;

    // Get delivery fee based on governorate
    const deliveryFee = getDeliveryFee();
    const total = subtotal + deliveryFee;

    // Update delivery fee display
    if (deliveryFee > 0) {
        checkoutDeliveryElement.textContent = formatPrice(deliveryFee);
    } else {
        checkoutDeliveryElement.textContent = 'يُحدد حسب المحافظة';
    }

    // Update total
    checkoutTotalElement.textContent = formatPrice(total);
}

function getDeliveryFee() {
    // Get governorate from current profile
    const defaultProfile = profiles.find(p => p.id == currentProfileId);

    if (!defaultProfile || !defaultProfile.governorate) {
        return 0; // No fee if no profile or governorate selected
    }

    const governorate = defaultProfile.governorate.toLowerCase();

    // Delivery fees
    if (governorate === 'basra' || governorate === 'البصرة') {
        return 3000; // البصرة: 3000 دينار
    } else {
        return 5000; // باقي المحافظات: 5000 دينار
    }
}

function validateCheckoutForm() {
    const name = document.getElementById('customer-name').value.trim();
    const phone = document.getElementById('customer-phone').value.trim();
    const address = document.getElementById('customer-address').value.trim();
    const paymentMethod = document.querySelector('input[name="payment-method"]:checked').value;

    // Basic validation
    if (!name) {
        showEnhancedNotification('يرجى إدخال الاسم الكامل', 'error', 'ri-user-line');
        return false;
    }

    if (!phone || !validatePhoneNumber(phone)) {
        showEnhancedNotification('يرجى إدخال رقم هاتف صحيح', 'error', 'ri-phone-line');
        return false;
    }

    if (!address || address.length < 10) {
        showEnhancedNotification('يرجى إدخال عنوان تفصيلي (10 أحرف على الأقل)', 'error', 'ri-map-pin-line');
        return false;
    }

    // Payment method is always cash on delivery - no additional validation needed
    if (paymentMethod !== 'cash') {
        showEnhancedNotification('طريقة الدفع الوحيدة المتاحة هي الدفع عند التسليم', 'error', 'ri-money-dollar-circle-line');
        return false;
    }

    return true;
}

function processOrder(orderData) {
    return new Promise((resolve, reject) => {
        // Simulate payment processing with order data
        console.log('Processing order:', orderData);
        setTimeout(() => {
            const success = Math.random() > 0.1; // 90% success rate

            if (success) {
                resolve({
                    orderId: 'ORD-' + Date.now(),
                    status: 'confirmed',
                    estimatedDelivery: new Date(Date.now() + 2 * 60 * 60 * 1000) // 2 hours from now
                });
            } else {
                reject(new Error('فشل في معالجة الدفع. يرجى المحاولة مرة أخرى.'));
            }
        }, 2000);
    });
}

function handleCheckoutSubmit(e) {
    e.preventDefault();

    try {
        if (!validateCheckoutForm()) return;

        // Show loading state
        const submitBtn = document.getElementById('place-order-btn');
        showButtonLoading(submitBtn);

        // Calculate totals including delivery
        const subtotal = cart.reduce((sum, item) => sum + (item.price * item.quantity), 0);
        const deliveryFee = getDeliveryFee();
        const total = subtotal + deliveryFee;

        // Collect order data
        const orderData = {
            id: 'ORD-' + Date.now(),
            items: cart.map(item => ({...item})),
            customer: {
                name: sanitizeInput(document.getElementById('customer-name').value.trim()),
                phone: sanitizeInput(document.getElementById('customer-phone').value.trim()),
                address: sanitizeInput(document.getElementById('customer-address').value.trim())
            },
            paymentMethod: document.querySelector('input[name="payment-method"]:checked').value,
            notes: sanitizeInput(document.getElementById('order-notes').value.trim()),
            subtotal: subtotal,
            deliveryFee: deliveryFee,
            total: total,
            timestamp: new Date().toISOString(),
            status: 'pending'
        };

        // Process order
        processOrder(orderData)
            .then(result => {
                // Save order
                const orders = JSON.parse(localStorage.getItem('velasweets_orders')) || [];
                orderData.id = result.orderId;
                orderData.status = result.status;
                orderData.estimatedDelivery = result.estimatedDelivery;
                orders.unshift(orderData);
                localStorage.setItem('velasweets_orders', JSON.stringify(orders));

                // Record sales analytics
                cart.forEach(item => {
                    recordProductSale(item.id, item.quantity);
                });

                // Add analytics activity
                addAnalyticsActivity('order', `تم تأكيد طلب جديد برقم ${result.orderId}`);

                // Add notification
                addNotification(`تم تأكيد طلبك برقم ${result.orderId}`, 'success', 'orders');

                // Clear cart
                cart = [];
                localStorage.removeItem('velasweets_cart');

                // Update UI
                updateCartUI();

                // Show success message
                showEnhancedNotification(`تم تأكيد طلبك بنجاح! رقم الطلب: ${result.orderId}`, 'success', 'ri-check-line');

                // Close modal
                closeCheckoutModal();
                closeCart();

                // Hide loading state
                hideButtonLoading(submitBtn);

            })
            .catch(error => {
                hideButtonLoading(submitBtn);
                showEnhancedNotification(error.message, 'error', 'ri-error-warning-line');
            });

    } catch (error) {
        handleError(error, 'handleCheckoutSubmit');
    }
}

// Mobile Optimizations
function initializeMobileOptimizations() {
    // Mobile navigation
    const mobileCartBtn = document.getElementById('mobile-cart-btn');
    const mobileFavoritesBtn = document.getElementById('mobile-favorites-btn');

    if (mobileCartBtn) {
        mobileCartBtn.addEventListener('click', openCart);
    }

    if (mobileFavoritesBtn) {
        mobileFavoritesBtn.addEventListener('click', openFavorites);
    }

    // Mobile profile
    const mobileProfileBtn = document.getElementById('mobile-profile-btn');
    if (mobileProfileBtn) {
        mobileProfileBtn.addEventListener('click', openMobileProfileModal);
    }

    const closeMobileProfileBtn = document.getElementById('close-mobile-profile-modal');
    if (closeMobileProfileBtn) {
        closeMobileProfileBtn.addEventListener('click', closeMobileProfileModal);
    }

    const mobileProfileSettingsBtn = document.getElementById('mobile-profile-settings-btn');
    if (mobileProfileSettingsBtn) {
        mobileProfileSettingsBtn.addEventListener('click', function() {
            closeMobileProfileModal();
            openProfileModal();
        });
    }

    const mobileOrdersTrackingBtn = document.getElementById('mobile-orders-tracking-btn');
    if (mobileOrdersTrackingBtn) {
        mobileOrdersTrackingBtn.addEventListener('click', function() {
            closeMobileProfileModal();
            openOrdersModal();
        });
    }

    // Close mobile profile modal when clicking outside
    if (mobileProfileModal) {
        mobileProfileModal.addEventListener('click', function(e) {
            if (e.target === mobileProfileModal) {
                closeMobileProfileModal();
            }
        });
    }

    // Update mobile counters
    updateMobileCounters();

    // Touch gestures for sidebars
    initializeTouchGestures();

    // Viewport height fix for mobile browsers
    fixMobileViewportHeight();

    // Prevent zoom on input focus (iOS)
    preventInputZoom();

    // Add touch ripple effects
    addTouchRippleEffects();
}

function updateMobileCounters() {
    const mobileCartCount = document.getElementById('mobile-cart-count');
    const mobileFavoritesCount = document.getElementById('mobile-favorites-count');

    if (mobileCartCount) {
        const totalItems = cart.reduce((sum, item) => sum + item.quantity, 0);
        mobileCartCount.textContent = totalItems;
        mobileCartCount.classList.toggle('hidden', totalItems === 0);
    }

    if (mobileFavoritesCount) {
        mobileFavoritesCount.textContent = favorites.length;
        mobileFavoritesCount.classList.toggle('hidden', favorites.length === 0);
    }
}

function initializeTouchGestures() {
    let startX = 0;
    let startY = 0;
    let currentX = 0;
    let currentY = 0;

    // Swipe to close sidebars
    [cartSidebar, favoritesSidebar].forEach(sidebar => {
        if (!sidebar) return;

        sidebar.addEventListener('touchstart', (e) => {
            startX = e.touches[0].clientX;
            startY = e.touches[0].clientY;
        }, { passive: true });

        sidebar.addEventListener('touchmove', (e) => {
            currentX = e.touches[0].clientX;
            currentY = e.touches[0].clientY;
        }, { passive: true });

        sidebar.addEventListener('touchend', () => {
            const diffX = startX - currentX;
            const diffY = Math.abs(startY - currentY);

            // Swipe left to close (for RTL layout)
            if (diffX > 50 && diffY < 100) {
                if (sidebar === cartSidebar) {
                    closeCart();
                } else if (sidebar === favoritesSidebar) {
                    closeFavorites();
                }
            }
        }, { passive: true });
    });
}

function fixMobileViewportHeight() {
    // Fix for mobile browsers that change viewport height when address bar shows/hides
    function setViewportHeight() {
        const vh = window.innerHeight * 0.01;
        document.documentElement.style.setProperty('--vh', `${vh}px`);
    }

    setViewportHeight();
    window.addEventListener('resize', setViewportHeight);
    window.addEventListener('orientationchange', () => {
        setTimeout(setViewportHeight, 100);
    });
}

function preventInputZoom() {
    // Prevent zoom on input focus for iOS devices
    const inputs = document.querySelectorAll('input, textarea, select');
    inputs.forEach(input => {
        input.addEventListener('focus', () => {
            if (window.innerWidth < 768) {
                const viewport = document.querySelector('meta[name="viewport"]');
                if (viewport) {
                    viewport.setAttribute('content', 'width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no');
                }
            }
        });

        input.addEventListener('blur', () => {
            if (window.innerWidth < 768) {
                const viewport = document.querySelector('meta[name="viewport"]');
                if (viewport) {
                    viewport.setAttribute('content', 'width=device-width, initial-scale=1.0');
                }
            }
        });
    });
}

function addTouchRippleEffects() {
    const buttons = document.querySelectorAll('button, .btn');

    buttons.forEach(button => {
        button.addEventListener('touchstart', function(e) {
            const ripple = document.createElement('span');
            const rect = this.getBoundingClientRect();
            const size = Math.max(rect.width, rect.height);
            const x = e.touches[0].clientX - rect.left - size / 2;
            const y = e.touches[0].clientY - rect.top - size / 2;

            ripple.style.width = ripple.style.height = size + 'px';
            ripple.style.left = x + 'px';
            ripple.style.top = y + 'px';
            ripple.classList.add('ripple-effect');

            this.appendChild(ripple);

            setTimeout(() => {
                if (ripple.parentElement) {
                    ripple.remove();
                }
            }, 600);
        }, { passive: true });
    });
}

// Update mobile counters when cart or favorites change
const originalUpdateCartUI = updateCartUI;
updateCartUI = function() {
    originalUpdateCartUI.call(this);
    updateMobileCounters();
};

const originalUpdateFavoritesUI = updateFavoritesUI;
updateFavoritesUI = function() {
    originalUpdateFavoritesUI.call(this);
    updateMobileCounters();
};

// Voice Search System removed as requested

// Search functionality removed as requested

// Sample notifications removed - real store only

// Add sample reviews for demonstration
function addSampleReviews() {
    const existingReviews = JSON.parse(localStorage.getItem('velasweets_reviews')) || [];

    // Only add sample reviews if none exist
    if (existingReviews.length === 0) {
        const sampleReviews = [
            {
                productId: 1,
                rating: 5,
                text: 'منتج رائع وطعم مميز جداً! التوصيل كان سريع والتغليف ممتاز. أنصح بالتجربة بقوة.',
                date: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(), // 2 days ago
                author: 'أحمد محمد'
            },
            {
                productId: 1,
                rating: 4,
                text: 'جودة عالية وطعم لذيذ، لكن السعر مرتفع قليلاً. بشكل عام راضي عن المنتج.',
                date: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString(), // 5 days ago
                author: 'فاطمة علي'
            },
            {
                productId: 2,
                rating: 5,
                text: 'أفضل مادلين كيك جربته! ناعم ولذيذ ومغطى بشوكولاتة فاخرة حقاً.',
                date: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString(), // 1 day ago
                author: 'سارة أحمد'
            },
            {
                productId: 2,
                rating: 5,
                text: 'ممتاز جداً! الطعم رائع والجودة عالية. سأطلب مرة أخرى بالتأكيد.',
                date: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString(), // 3 days ago
                author: 'محمد حسن'
            },
            {
                productId: 3,
                rating: 4,
                text: 'حلى لذيذ وطبقات متعددة كما هو موصوف. الفول السوداني يضيف نكهة مميزة.',
                date: new Date(Date.now() - 4 * 24 * 60 * 60 * 1000).toISOString(), // 4 days ago
                author: 'نور الهدى'
            }
        ];

        localStorage.setItem('velasweets_reviews', JSON.stringify(sampleReviews));
    }
}

// Initialize everything on page load
document.addEventListener('DOMContentLoaded', function() {
    // Clean demo data first (only run once for real store)
    cleanDemoData();

    // Show loading state
    showInitialLoading();

    // Initialize DOM elements first
    initializeDOM();

    // Initialize event listeners
    setupEventListeners();

    // Apply theme
    initializeTheme();

    // Sample data removed - real store only

    // Simulate loading time for better UX
    setTimeout(() => {
        // Update UI
        updateCartUI();
        updateFavoritesUI();
        updateNotificationsUI();

        // Initialize favorites buttons
        setTimeout(updateFavoriteButtons, 100);

        // Update product ratings on main page
        setTimeout(updateMainPageRatings, 200);

        // Hide loading state
        hideInitialLoading();

        // Add entrance animations to products
        animateProductsEntrance();

        // Add ripple effects to buttons
        addRippleEffectsToButtons();

    }, 1000); // 1 second loading simulation

    // Initialize additional features
    setTimeout(() => {

        initializeSecurityAndPerformance();
        initializeMobileOptimizations();
    }, 1100);
});

function showInitialLoading() {
    const productsGrid = document.querySelector('.grid');
    if (productsGrid) {
        showSkeletonLoader(productsGrid, 3);
    }
}

function hideInitialLoading() {
    // This will be handled by animateProductsEntrance
}

function animateProductsEntrance() {
    const productCards = document.querySelectorAll('.grid > div');
    productCards.forEach((card, index) => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(20px)';

        setTimeout(() => {
            card.style.transition = 'all 0.6s ease';
            card.style.opacity = '1';
            card.style.transform = 'translateY(0)';
        }, index * 200); // Stagger animation
    });
}

function addRippleEffectsToButtons() {
    const buttons = document.querySelectorAll('button:not(.no-ripple)');
    buttons.forEach(button => {
        addRippleEffect(button);
    });
}



// Custom Alert and Confirm Functions
function customAlert(message, title = 'تنبيه', type = 'info') {
    return new Promise((resolve) => {
        const alertModal = document.getElementById('custom-alert');
        const alertIcon = document.getElementById('alert-icon');
        const alertIconClass = document.getElementById('alert-icon-class');
        const alertTitle = document.getElementById('alert-title');
        const alertMessage = document.getElementById('alert-message');
        const alertOk = document.getElementById('alert-ok');

        // Set icon and colors based on type
        const types = {
            success: {
                icon: 'ri-check-line',
                bgColor: 'bg-green-100 dark:bg-green-900/20',
                iconColor: 'text-green-600 dark:text-green-400'
            },
            error: {
                icon: 'ri-error-warning-line',
                bgColor: 'bg-red-100 dark:bg-red-900/20',
                iconColor: 'text-red-600 dark:text-red-400'
            },
            warning: {
                icon: 'ri-alert-line',
                bgColor: 'bg-yellow-100 dark:bg-yellow-900/20',
                iconColor: 'text-yellow-600 dark:text-yellow-400'
            },
            info: {
                icon: 'ri-information-line',
                bgColor: 'bg-blue-100 dark:bg-blue-900/20',
                iconColor: 'text-blue-600 dark:text-blue-400'
            }
        };

        const typeConfig = types[type] || types.info;

        alertIcon.className = `w-16 h-16 mx-auto mb-4 rounded-full flex items-center justify-center ${typeConfig.bgColor}`;
        alertIconClass.className = `text-3xl ${typeConfig.icon} ${typeConfig.iconColor}`;
        alertTitle.textContent = title;
        alertMessage.textContent = message;

        // Show modal
        alertModal.classList.remove('hidden');
        setTimeout(() => {
            alertModal.querySelector('.bg-white').classList.remove('scale-95');
            alertModal.querySelector('.bg-white').classList.add('scale-100');
        }, 10);

        // Handle OK click
        const handleOk = () => {
            alertModal.querySelector('.bg-white').classList.add('scale-95');
            setTimeout(() => {
                alertModal.classList.add('hidden');
                resolve(true);
            }, 200);
            alertOk.removeEventListener('click', handleOk);
        };

        alertOk.addEventListener('click', handleOk);
    });
}

function customConfirm(message, title = 'تأكيد العملية') {
    return new Promise((resolve) => {
        const confirmModal = document.getElementById('custom-confirm');
        const confirmTitle = document.getElementById('confirm-title');
        const confirmMessage = document.getElementById('confirm-message');
        const confirmOk = document.getElementById('confirm-ok');
        const confirmCancel = document.getElementById('confirm-cancel');

        confirmTitle.textContent = title;
        confirmMessage.textContent = message;

        // Show modal
        confirmModal.classList.remove('hidden');
        setTimeout(() => {
            confirmModal.querySelector('.bg-white').classList.remove('scale-95');
            confirmModal.querySelector('.bg-white').classList.add('scale-100');
        }, 10);

        // Handle clicks
        const handleOk = () => {
            confirmModal.querySelector('.bg-white').classList.add('scale-95');
            setTimeout(() => {
                confirmModal.classList.add('hidden');
                resolve(true);
            }, 200);
            cleanup();
        };

        const handleCancel = () => {
            confirmModal.querySelector('.bg-white').classList.add('scale-95');
            setTimeout(() => {
                confirmModal.classList.add('hidden');
                resolve(false);
            }, 200);
            cleanup();
        };

        const cleanup = () => {
            confirmOk.removeEventListener('click', handleOk);
            confirmCancel.removeEventListener('click', handleCancel);
        };

        confirmOk.addEventListener('click', handleOk);
        confirmCancel.addEventListener('click', handleCancel);
    });
}

function customPrompt(message, title = 'إدخال البيانات', defaultValue = '') {
    return new Promise((resolve) => {
        const promptModal = document.getElementById('custom-prompt');
        const promptTitle = document.getElementById('prompt-title');
        const promptMessage = document.getElementById('prompt-message');
        const promptInput = document.getElementById('prompt-input');
        const promptOk = document.getElementById('prompt-ok');
        const promptCancel = document.getElementById('prompt-cancel');

        promptTitle.textContent = title;
        promptMessage.textContent = message;
        promptInput.value = defaultValue;

        // Show modal
        promptModal.classList.remove('hidden');
        setTimeout(() => {
            promptModal.querySelector('.bg-white').classList.remove('scale-95');
            promptModal.querySelector('.bg-white').classList.add('scale-100');
            promptInput.focus();
            promptInput.select();
        }, 10);

        // Handle clicks
        const handleOk = () => {
            const value = promptInput.value.trim();
            promptModal.querySelector('.bg-white').classList.add('scale-95');
            setTimeout(() => {
                promptModal.classList.add('hidden');
                resolve(value || null);
            }, 200);
            cleanup();
        };

        const handleCancel = () => {
            promptModal.querySelector('.bg-white').classList.add('scale-95');
            setTimeout(() => {
                promptModal.classList.add('hidden');
                resolve(null);
            }, 200);
            cleanup();
        };

        const handleEnter = (e) => {
            if (e.key === 'Enter') {
                handleOk();
            } else if (e.key === 'Escape') {
                handleCancel();
            }
        };

        const cleanup = () => {
            promptOk.removeEventListener('click', handleOk);
            promptCancel.removeEventListener('click', handleCancel);
            promptInput.removeEventListener('keydown', handleEnter);
        };

        promptOk.addEventListener('click', handleOk);
        promptCancel.addEventListener('click', handleCancel);
        promptInput.addEventListener('keydown', handleEnter);
    });
}

// Override default alert, confirm, and prompt
window.alert = customAlert;
window.confirm = customConfirm;
window.prompt = customPrompt;

// Initialize the app when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
});

// Also initialize if DOM is already loaded
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initializeApp);
} else {
    initializeApp();
}

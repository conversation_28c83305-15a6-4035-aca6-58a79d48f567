﻿// Cart Module - إدارة السلة
const CartModule = {
    cart: [],
    
    init: function() {
        this.loadCart();
        this.setupCartEvents();
        this.updateCartDisplay();
    },
    
    loadCart: function() {
        try {
            const savedCart = localStorage.getItem('velasweets_cart');
            if (savedCart) {
                this.cart = JSON.parse(savedCart);
            }
        } catch (error) {
            console.error('Error loading cart:', error);
            this.cart = [];
        }
    },
    
    saveCart: function() {
        try {
            localStorage.setItem('velasweets_cart', JSON.stringify(this.cart));
        } catch (error) {
            console.error('Error saving cart:', error);
        }
    },
    
    addItem: function(product, quantity = 1) {
        const existingItem = this.cart.find(item => item.id === product.id);
        
        if (existingItem) {
            existingItem.quantity += quantity;
        } else {
            this.cart.push({
                ...product,
                quantity: quantity,
                addedAt: Date.now()
            });
        }
        
        this.saveCart();
        this.updateCartDisplay();
        this.showNotification(تم إضافة  إلى السلة, 'success');
    },
    
    removeItem: function(productId) {
        const itemIndex = this.cart.findIndex(item => item.id === productId);
        if (itemIndex > -1) {
            const removedItem = this.cart.splice(itemIndex, 1)[0];
            this.saveCart();
            this.updateCartDisplay();
            this.showNotification(تم حذف  من السلة, 'info');
        }
    },
    
    updateQuantity: function(productId, quantity) {
        const item = this.cart.find(item => item.id === productId);
        if (item) {
            if (quantity <= 0) {
                this.removeItem(productId);
            } else {
                item.quantity = quantity;
                this.saveCart();
                this.updateCartDisplay();
            }
        }
    },
    
    clearCart: function() {
        this.cart = [];
        this.saveCart();
        this.updateCartDisplay();
        this.showNotification('تم تفريغ السلة', 'info');
    },
    
    getTotal: function() {
        return this.cart.reduce((total, item) => total + (item.price * item.quantity), 0);
    },
    
    getItemCount: function() {
        return this.cart.reduce((count, item) => count + item.quantity, 0);
    },
    
    updateCartDisplay: function() {
        // Update cart counter
        const cartCounter = document.getElementById('cart-counter');
        if (cartCounter) {
            const count = this.getItemCount();
            cartCounter.textContent = count;
            cartCounter.style.display = count > 0 ? 'block' : 'none';
        }
        
        // Update cart sidebar
        this.renderCartSidebar();
    },
    
    renderCartSidebar: function() {
        const cartItems = document.getElementById('cart-items');
        const cartTotal = document.getElementById('cart-total');
        
        if (!cartItems || !cartTotal) return;
        
        if (this.cart.length === 0) {
            cartItems.innerHTML = '<p class="text-center text-gray-500 py-8">السلة فارغة</p>';
            cartTotal.textContent = '0 د.ع';
            return;
        }
        
        cartItems.innerHTML = this.cart.map(item => 
            <div class="flex items-center gap-3 p-3 border-b border-gray-200 dark:border-gray-700">
                <img src="" alt="" class="w-12 h-12 object-cover rounded">
                <div class="flex-1">
                    <h4 class="font-medium text-sm"></h4>
                    <p class="text-xs text-gray-500"> د.ع</p>
                </div>
                <div class="flex items-center gap-2">
                    <button onclick="CartModule.updateQuantity(, )" 
                            class="w-6 h-6 rounded-full bg-gray-200 dark:bg-gray-700 flex items-center justify-center text-xs">-</button>
                    <span class="text-sm font-medium"></span>
                    <button onclick="CartModule.updateQuantity(, )" 
                            class="w-6 h-6 rounded-full bg-gray-200 dark:bg-gray-700 flex items-center justify-center text-xs">+</button>
                </div>
                <button onclick="CartModule.removeItem()" 
                        class="text-red-500 hover:text-red-700 text-sm">
                    <i class="ri-delete-bin-line"></i>
                </button>
            </div>
        ).join('');
        
        cartTotal.textContent = this.getTotal().toLocaleString() + ' د.ع';
    },
    
    setupCartEvents: function() {
        // Cart toggle
        const cartToggle = document.getElementById('cart-toggle');
        const cartSidebar = document.getElementById('cart-sidebar');
        const cartOverlay = document.getElementById('cart-overlay');
        
        if (cartToggle && cartSidebar) {
            cartToggle.addEventListener('click', () => {
                cartSidebar.classList.remove('translate-x-full');
                if (cartOverlay) cartOverlay.classList.remove('hidden');
            });
        }
        
        // Close cart
        const closeCart = document.getElementById('close-cart');
        if (closeCart && cartSidebar) {
            closeCart.addEventListener('click', () => {
                cartSidebar.classList.add('translate-x-full');
                if (cartOverlay) cartOverlay.classList.add('hidden');
            });
        }
        
        // Overlay click to close
        if (cartOverlay && cartSidebar) {
            cartOverlay.addEventListener('click', () => {
                cartSidebar.classList.add('translate-x-full');
                cartOverlay.classList.add('hidden');
            });
        }
    },
    
    showNotification: function(message, type = 'info') {
        if (typeof showEnhancedNotification === 'function') {
            const icons = {
                success: 'ri-check-line',
                error: 'ri-error-warning-line',
                info: 'ri-information-line'
            };
            showEnhancedNotification(message, type, icons[type]);
        }
    }
};

// Register the module
if (typeof VelaSweets !== 'undefined') {
    VelaSweets.registerModule('cart', CartModule);
}

// Export for global use
window.CartModule = CartModule;

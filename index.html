<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">

    <!-- SEO Meta Tags -->
    <title>VelaSweets - أفضل الحلويات العراقية الأصيلة | متجر الحلويات الشرقية</title>
    <meta name="description" content="اكتشف أفضل الحلويات العراقية الأصيلة في VelaSweets. نقدم الكنافة، البقلاوة، والمعمول الطازج بأعلى جودة وأفضل الأسعار. توصيل سريع في بغداد والمحافظات.">
    <meta name="keywords" content="حلويات عراقية, كنافة, بقلاوة, معمول, حلويات شرقية, متجر حلويات, توصيل حلويات, بغداد, العراق">
    <meta name="author" content="VelaSweets">
    <meta name="robots" content="index, follow">
    <meta name="language" content="Arabic">

    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="VelaSweets - أفضل الحلويات العراقية الأصيلة">
    <meta property="og:description" content="اكتشف أفضل الحلويات العراقية الأصيلة في VelaSweets. كنافة، بقلاوة، ومعمول طازج بأعلى جودة.">
    <meta property="og:image" content="https://images.unsplash.com/photo-1578985545062-69928b1d9587?w=1200&h=630&fit=crop&crop=center">
    <meta property="og:url" content="https://velasweets.com">
    <meta property="og:type" content="website">
    <meta property="og:site_name" content="VelaSweets">
    <meta property="og:locale" content="ar_IQ">

    <!-- Twitter Card Meta Tags -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="VelaSweets - أفضل الحلويات العراقية">
    <meta name="twitter:description" content="اكتشف أفضل الحلويات العراقية الأصيلة. كنافة، بقلاوة، ومعمول طازج بأعلى جودة.">
    <meta name="twitter:image" content="https://images.unsplash.com/photo-1578985545062-69928b1d9587?w=1200&h=630&fit=crop&crop=center">

    <!-- Additional Meta Tags -->
    <meta name="theme-color" content="#ec4899">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-title" content="VelaSweets">

    <!-- Canonical URL -->
    <link rel="canonical" href="https://velasweets.com">

    <!-- Preconnect for Performance -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link rel="preconnect" href="https://cdn.jsdelivr.net">

    <!-- Tailwind CSS - Production Ready -->
    <script src="https://cdn.tailwindcss.com?plugins=forms,typography,aspect-ratio"></script>
    <script>
        // Suppress Tailwind development warnings and configure
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    fontFamily: {
                        'cairo': ['Cairo', 'sans-serif']
                    }
                }
            }
        };

        // Suppress console warnings for production
        if (typeof console !== 'undefined') {
            const originalWarn = console.warn;
            console.warn = function(...args) {
                const message = args.join(' ');
                if (message.includes('cdn.tailwindcss.com should not be used in production') ||
                    message.includes('line-clamp') ||
                    message.includes('Remove it from the `plugins` array')) {
                    return; // Suppress these specific warnings
                }
                originalWarn.apply(console, args);
            };
        }
    </script>

    <!-- RemixIcon -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/remixicon@4.1.0/fonts/remixicon.css">

    <!-- Google Fonts - Arabic -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- Custom CSS -->
    <link rel="stylesheet" href="global.css">

    <!-- Structured Data (JSON-LD) -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "Store",
        "name": "VelaSweets",
        "description": "متجر الحلويات العراقية الأصيلة - كنافة، بقلاوة، ومعمول طازج",
        "url": "https://velasweets.com",
        "logo": "https://images.unsplash.com/photo-1578985545062-69928b1d9587?w=200&h=200&fit=crop&crop=center",
        "image": "https://images.unsplash.com/photo-1578985545062-69928b1d9587?w=800&h=600&fit=crop&crop=center",
        "telephone": "+964-123-456-789",
        "address": {
            "@type": "PostalAddress",
            "streetAddress": "شارع الرشيد",
            "addressLocality": "بغداد",
            "addressCountry": "IQ"
        },
        "geo": {
            "@type": "GeoCoordinates",
            "latitude": "33.3152",
            "longitude": "44.3661"
        },
        "openingHours": "Mo-Su 08:00-22:00",
        "priceRange": "$$",
        "servesCuisine": "Middle Eastern",
        "acceptsReservations": false,
        "hasMenu": "https://velasweets.com/#products"
    }
    </script>
</head>
<body class="font-cairo bg-white dark:bg-gray-900 text-gray-900 dark:text-white transition-colors duration-300">
    
    <!-- Header -->
    <header class="bg-white dark:bg-gray-800 shadow-lg sticky top-0 z-50 transition-colors duration-300">
        <div class="container mx-auto px-4 py-4">
            <div class="flex items-center justify-between">
                <!-- Navigation -->
                <div class="flex items-center">
                    <nav class="hidden md:flex space-x-6 space-x-reverse">
                        <a href="#home" class="hover:text-pink-600 dark:hover:text-pink-400 transition-colors">الرئيسية</a>
                        <a href="#products" class="hover:text-pink-600 dark:hover:text-pink-400 transition-colors">المنتجات</a>
                        <a href="#contact" class="hover:text-pink-600 dark:hover:text-pink-400 transition-colors">مركز المعلومات</a>
                    </nav>


                </div>
                
                <!-- Icons -->
                <div class="flex items-center space-x-4 space-x-reverse">
                    <!-- Notifications -->
                    <div class="relative">
                        <button id="notifications-btn" class="relative p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors">
                            <i class="ri-notification-line text-xl"></i>
                            <span id="notifications-count" class="absolute -top-1 -right-1 bg-red-500/90 text-white text-xs font-medium rounded-lg px-1.5 py-0.5 shadow-sm border border-red-400/50 hidden">0</span>
                        </button>

                        <!-- Notifications Dropdown -->
                        <div id="notifications-dropdown" class="absolute top-full right-0 mt-2 w-80 max-w-sm bg-white dark:bg-gray-800 rounded-xl shadow-2xl border border-gray-200 dark:border-gray-700 z-50 hidden">
                            <!-- Header -->
                            <div class="flex items-center justify-between p-3 border-b border-gray-200 dark:border-gray-700">
                                <h3 class="text-base font-semibold text-gray-900 dark:text-white">الإشعارات</h3>
                                <div class="flex items-center gap-1">
                                    <button id="mark-all-read" class="text-xs text-pink-600 dark:text-pink-400 hover:underline">
                                        قراءة الكل
                                    </button>
                                    <button id="close-notifications" class="p-1 hover:bg-gray-100 dark:hover:bg-gray-700 rounded transition-colors" title="إغلاق">
                                        <i class="ri-close-line text-sm text-gray-600 dark:text-gray-400"></i>
                                    </button>
                                </div>
                            </div>

                            <!-- Filter Tabs -->
                            <div class="flex border-b border-gray-200 dark:border-gray-700">
                                <button class="notification-tab active flex-1 py-2 px-2 text-xs font-medium text-center border-b-2 border-pink-600 text-pink-600" data-filter="all">
                                    الكل
                                </button>
                                <button class="notification-tab flex-1 py-2 px-2 text-xs font-medium text-center border-b-2 border-transparent text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300" data-filter="orders">
                                    الطلبات
                                </button>
                                <button class="notification-tab flex-1 py-2 px-2 text-xs font-medium text-center border-b-2 border-transparent text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300" data-filter="offers">
                                    العروض
                                </button>
                                <button class="notification-tab flex-1 py-2 px-2 text-xs font-medium text-center border-b-2 border-transparent text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300" data-filter="updates">
                                    التحديثات
                                </button>
                            </div>

                            <!-- Notifications List -->
                            <div id="notifications-list" class="max-h-64 overflow-y-auto">
                                <!-- Notifications will be populated by JavaScript -->
                            </div>

                            <!-- Footer -->
                            <div class="p-3 border-t border-gray-200 dark:border-gray-700">
                                <button id="clear-all-notifications" class="w-full text-xs text-gray-600 dark:text-gray-400 hover:text-red-600 dark:hover:text-red-400 transition-colors">
                                    مسح جميع الإشعارات
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Theme Toggle -->
                    <button id="theme-toggle" class="p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors">
                        <i class="ri-moon-line dark:hidden text-xl"></i>
                        <i class="ri-sun-line hidden dark:block text-xl"></i>
                    </button>

                    <!-- Language Selector -->
                    <div class="relative">
                        <button id="language-btn" class="p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors" title="اختيار اللغة">
                            <i class="ri-global-line text-xl"></i>
                        </button>

                        <!-- Language Dropdown -->
                        <div id="language-dropdown" class="absolute top-full right-0 mt-2 w-40 bg-white dark:bg-gray-800 rounded-xl shadow-2xl border border-gray-200 dark:border-gray-700 z-50 hidden">
                            <div class="py-2">
                                <button class="language-option w-full text-right px-4 py-3 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors flex items-center gap-3 active" data-lang="ar">
                                    <span class="text-lg">🇮🇶</span>
                                    <span class="text-gray-900 dark:text-white">العربية</span>
                                    <i class="ri-check-line text-pink-600 dark:text-pink-400 mr-auto"></i>
                                </button>
                                <button class="language-option w-full text-right px-4 py-3 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors flex items-center gap-3" data-lang="en">
                                    <span class="text-lg">🇺🇸</span>
                                    <span class="text-gray-900 dark:text-white">English</span>
                                </button>
                                <button class="language-option w-full text-right px-4 py-3 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors flex items-center gap-3" data-lang="ku">
                                    <span class="text-lg">🏴</span>
                                    <span class="text-gray-900 dark:text-white">کوردی</span>
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Profile Menu -->
                    <div class="relative">
                        <button id="profile-menu-btn" class="p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors" title="الملف الشخصي">
                            <i class="ri-user-line text-xl"></i>
                        </button>

                        <!-- Profile Dropdown -->
                        <div id="profile-dropdown" class="absolute top-full right-0 mt-2 w-48 bg-white dark:bg-gray-800 rounded-xl shadow-2xl border border-gray-200 dark:border-gray-700 z-50 hidden">
                            <div class="py-2">
                                <button id="profile-btn" class="w-full text-right px-4 py-3 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors flex items-center gap-3">
                                    <i class="ri-user-line text-gray-600 dark:text-gray-400"></i>
                                    <span class="text-gray-900 dark:text-white">الملف الشخصي</span>
                                </button>
                                <button id="orders-btn" class="w-full text-right px-4 py-3 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors flex items-center gap-3">
                                    <i class="ri-truck-line text-gray-600 dark:text-gray-400"></i>
                                    <span class="text-gray-900 dark:text-white">تتبع الطلبات</span>
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Favorites -->
                    <button id="favorites-btn" class="relative p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors">
                        <i class="ri-heart-line text-xl"></i>
                        <span id="favorites-count" class="absolute -top-1 -right-1 bg-pink-500/90 text-white text-xs font-medium rounded-lg px-1.5 py-0.5 shadow-sm border border-pink-400/50 hidden">0</span>
                    </button>

                    <!-- Cart -->
                    <button id="cart-btn" class="relative p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors">
                        <i class="ri-shopping-cart-line text-xl"></i>
                        <span id="cart-count" class="absolute -top-1 -right-1 bg-pink-500/90 text-white text-xs font-medium rounded-lg px-1.5 py-0.5 shadow-sm border border-pink-400/50 hidden">0</span>
                    </button>
                </div>
            </div>
        </div>
    </header>

    <!-- Hero Section -->
    <section id="home" class="bg-gradient-to-br from-pink-50 to-purple-50 dark:from-gray-800 dark:to-gray-900 py-20" role="banner">
        <div class="container mx-auto px-4 text-center">
            <h1 class="text-5xl md:text-6xl font-bold text-gray-900 dark:text-white mb-6">
                VelaSweets - أفضل الحلويات العراقية
            </h1>
            <p class="text-xl md:text-2xl text-gray-600 dark:text-gray-300 mb-8 max-w-2xl mx-auto">
                متجر الحلويات العراقية الأصيلة - نقدم لكم الكنافة، البقلاوة، والمعمول الطازج المصنوع بحب وعناية خاصة
            </p>
            <div class="flex flex-col sm:flex-row gap-4 justify-center">
                <button onclick="scrollToSection('products')" class="bg-pink-600 hover:bg-pink-700 text-white px-8 py-3 rounded-lg font-semibold transition-colors" aria-label="انتقل إلى قسم المنتجات">
                    شاهد المنتجات
                </button>
                <button onclick="scrollToSection('about')" class="border-2 border-pink-600 text-pink-600 hover:bg-pink-600 hover:text-white px-8 py-3 rounded-lg font-semibold transition-colors" aria-label="انتقل إلى قسم من نحن">
                    من نحن
                </button>
            </div>
        </div>
    </section>

    <!-- Products Section -->
    <section id="products" class="py-20 bg-white dark:bg-gray-900" role="main">
        <div class="container mx-auto px-4">
            <h2 class="text-4xl font-bold text-center mb-12 text-gray-900 dark:text-white">
                منتجاتنا المميزة - حلويات عراقية أصيلة
            </h2>

            <!-- Products will be dynamically loaded by JavaScript -->
            <div class="product-grid grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8" id="products-container">
                <!-- Loading skeleton while products load -->
                <div class="product-card skeleton-card">
                    <div class="skeleton skeleton-image h-64 mb-4"></div>
                    <div class="p-6">
                        <div class="skeleton skeleton-text h-6 mb-2"></div>
                        <div class="skeleton skeleton-text h-4 mb-4"></div>
                        <div class="skeleton skeleton-text h-8 mb-4 w-1/2"></div>
                        <div class="flex gap-2">
                            <div class="skeleton skeleton-button flex-1"></div>
                            <div class="skeleton skeleton-button flex-1"></div>
                        </div>
                    </div>
                </div>
                <div class="product-card skeleton-card">
                    <div class="skeleton skeleton-image h-64 mb-4"></div>
                    <div class="p-6">
                        <div class="skeleton skeleton-text h-6 mb-2"></div>
                        <div class="skeleton skeleton-text h-4 mb-4"></div>
                        <div class="skeleton skeleton-text h-8 mb-4 w-1/2"></div>
                        <div class="flex gap-2">
                            <div class="skeleton skeleton-button flex-1"></div>
                            <div class="skeleton skeleton-button flex-1"></div>
                        </div>
                    </div>
                </div>
                <div class="product-card skeleton-card">
                    <div class="skeleton skeleton-image h-64 mb-4"></div>
                    <div class="p-6">
                        <div class="skeleton skeleton-text h-6 mb-2"></div>
                        <div class="skeleton skeleton-text h-4 mb-4"></div>
                        <div class="skeleton skeleton-text h-8 mb-4 w-1/2"></div>
                        <div class="flex gap-2">
                            <div class="skeleton skeleton-button flex-1"></div>
                            <div class="skeleton skeleton-button flex-1"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- About Section -->
    <section id="about" class="py-20 bg-gray-50 dark:bg-gray-800">
        <div class="container mx-auto px-4">
            <h2 class="text-4xl font-bold text-center mb-12 text-gray-900 dark:text-white">
                من نحن
            </h2>
            
            <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                <!-- Quality -->
                <div class="text-center p-8 bg-white dark:bg-gray-700 rounded-xl shadow-lg">
                    <div class="text-6xl mb-4">🏆</div>
                    <h3 class="text-xl font-semibold mb-4 text-gray-900 dark:text-white">جودة عالية</h3>
                    <p class="text-gray-600 dark:text-gray-300">نستخدم أجود المكونات الطبيعية لضمان أفضل طعم وجودة</p>
                </div>
                
                <!-- Fast Delivery -->
                <div class="text-center p-8 bg-white dark:bg-gray-700 rounded-xl shadow-lg">
                    <div class="text-6xl mb-4">🚚</div>
                    <h3 class="text-xl font-semibold mb-4 text-gray-900 dark:text-white">توصيل سريع</h3>
                    <p class="text-gray-600 dark:text-gray-300">توصيل سريع وآمن لجميع المحافظات العراقية</p>
                </div>
                
                <!-- Excellent Service -->
                <div class="text-center p-8 bg-white dark:bg-gray-700 rounded-xl shadow-lg">
                    <div class="text-6xl mb-4">📞</div>
                    <h3 class="text-xl font-semibold mb-4 text-gray-900 dark:text-white">خدمة ممتازة</h3>
                    <p class="text-gray-600 dark:text-gray-300">فريق خدمة عملاء متاح دائماً لمساعدتكم</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Contact Section -->
    <section id="contact" class="py-20 bg-white dark:bg-gray-900">
        <div class="container mx-auto px-4">
            <h2 class="text-4xl font-bold text-center mb-12 text-gray-900 dark:text-white">
                تواصل معنا
            </h2>
            
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-12">
                <!-- Contact Info -->
                <div class="space-y-8">
                    <div class="flex items-center space-x-4 space-x-reverse">
                        <div class="bg-pink-100 dark:bg-pink-900 p-3 rounded-lg">
                            <i class="ri-phone-line text-pink-600 dark:text-pink-400 text-xl"></i>
                        </div>
                        <div>
                            <h3 class="font-semibold text-gray-900 dark:text-white">الهاتف</h3>
                            <p class="text-gray-600 dark:text-gray-300">+964 ************</p>
                        </div>
                    </div>
                    
                    <div class="flex items-center space-x-4 space-x-reverse">
                        <div class="bg-pink-100 dark:bg-pink-900 p-3 rounded-lg">
                            <i class="ri-mail-line text-pink-600 dark:text-pink-400 text-xl"></i>
                        </div>
                        <div>
                            <h3 class="font-semibold text-gray-900 dark:text-white">الإيميل</h3>
                            <p class="text-gray-600 dark:text-gray-300"><EMAIL></p>
                        </div>
                    </div>
                    
                    <div class="flex items-center space-x-4 space-x-reverse">
                        <div class="bg-pink-100 dark:bg-pink-900 p-3 rounded-lg">
                            <i class="ri-map-pin-line text-pink-600 dark:text-pink-400 text-xl"></i>
                        </div>
                        <div>
                            <h3 class="font-semibold text-gray-900 dark:text-white">العنوان</h3>
                            <p class="text-gray-600 dark:text-gray-300">البصرة، العراق</p>
                        </div>
                    </div>
                    
                    <!-- Social Media -->
                    <div class="pt-8">
                        <h3 class="font-semibold text-gray-900 dark:text-white mb-4">تابعونا على</h3>
                        <div class="flex space-x-4 space-x-reverse">
                            <a href="#" class="bg-blue-600 hover:bg-blue-700 text-white p-3 rounded-lg transition-colors">
                                <i class="ri-facebook-fill text-xl"></i>
                            </a>
                            <a href="#" class="bg-pink-600 hover:bg-pink-700 text-white p-3 rounded-lg transition-colors">
                                <i class="ri-instagram-line text-xl"></i>
                            </a>
                            <a href="https://www.tiktok.com/@velaswets" target="_blank" class="bg-gray-900 hover:bg-gray-800 text-white p-3 rounded-lg transition-colors">
                                <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                                    <path d="M19.59 6.69a4.83 4.83 0 0 1-3.77-4.25V2h-3.45v13.67a2.89 2.89 0 0 1-5.2 1.74 2.89 2.89 0 0 1 2.31-4.64 2.93 2.93 0 0 1 .88.13V9.4a6.84 6.84 0 0 0-1-.05A6.33 6.33 0 0 0 5 20.1a6.34 6.34 0 0 0 10.86-4.43v-7a8.16 8.16 0 0 0 4.77 1.52v-3.4a4.85 4.85 0 0 1-1-.1z"/>
                                </svg>
                            </a>
                        </div>
                    </div>
                </div>
                
                <!-- Contact Form -->
                <div class="bg-gray-50 dark:bg-gray-800 p-8 rounded-xl">
                    <form id="contact-form" class="space-y-6">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">الاسم</label>
                            <input type="text" class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white">
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">الإيميل</label>
                            <input type="email" class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white">
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">الرسالة</label>
                            <textarea rows="4" class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"></textarea>
                        </div>
                        
                        <button type="submit" class="w-full bg-pink-600 hover:bg-pink-700 text-white py-3 rounded-lg font-semibold transition-colors">
                            إرسال الرسالة
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-gray-900 text-white py-12">
        <div class="container mx-auto px-4">
            <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                <!-- Store Info -->
                <div>
                    <h3 class="text-2xl font-bold text-pink-400 mb-4">VelaSweets</h3>
                    <p class="text-gray-300 mb-4">متجر الحلويات الفاخرة - نقدم لكم أجود أنواع الحلويات المصنوعة بحب وعناية</p>
                    <div class="flex space-x-4 space-x-reverse">
                        <a href="https://www.facebook.com/velaswets" target="_blank" class="text-gray-400 hover:text-pink-400 transition-colors">
                            <i class="ri-facebook-fill text-xl"></i>
                        </a>
                        <a href="https://www.instagram.com/velaswets" target="_blank" class="text-gray-400 hover:text-pink-400 transition-colors">
                            <i class="ri-instagram-line text-xl"></i>
                        </a>
                        <a href="https://www.tiktok.com/@velasweets" target="_blank" class="text-gray-400 hover:text-pink-400 transition-colors">
                            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M19.59 6.69a4.83 4.83 0 0 1-3.77-4.25V2h-3.45v13.67a2.89 2.89 0 0 1-5.2 1.74 2.89 2.89 0 0 1 2.31-4.64 2.93 2.93 0 0 1 .88.13V9.4a6.84 6.84 0 0 0-1-.05A6.33 6.33 0 0 0 5 20.1a6.34 6.34 0 0 0 10.86-4.43v-7a8.16 8.16 0 0 0 4.77 1.52v-3.4a4.85 4.85 0 0 1-1-.1z"/>
                            </svg>
                        </a>
                    </div>
                </div>
                
                <!-- Quick Links -->
                <div>
                    <h4 class="text-lg font-semibold mb-4">روابط سريعة</h4>
                    <ul class="space-y-2">
                        <li><a href="#home" class="text-gray-300 hover:text-pink-400 transition-colors">الرئيسية</a></li>
                        <li><a href="#products" class="text-gray-300 hover:text-pink-400 transition-colors">المنتجات</a></li>
                        <li><a href="#about" class="text-gray-300 hover:text-pink-400 transition-colors">من نحن</a></li>
                        <li><a href="#contact" class="text-gray-300 hover:text-pink-400 transition-colors">تواصل معنا</a></li>
                    </ul>
                </div>
                
                <!-- Contact Info -->
                <div>
                    <h4 class="text-lg font-semibold mb-4">معلومات الاتصال</h4>
                    <div class="space-y-2 text-gray-300">
                        <p><i class="ri-phone-line ml-2"></i>+964 ************</p>
                        <p><i class="ri-mail-line ml-2"></i><EMAIL></p>
                        <p><i class="ri-map-pin-line ml-2"></i>البصرة، العراق</p>
                    </div>
                </div>
            </div>
            
            <div class="border-t border-gray-800 mt-8 pt-8 text-center text-gray-400">
                <div class="flex justify-center space-x-4 space-x-reverse mb-4 text-sm">
                    <button id="privacy-policy-btn" class="hover:text-pink-400 transition-colors">
                        سياسة الخصوصية
                    </button>
                    <span>|</span>
                    <button id="terms-of-service-btn" class="hover:text-pink-400 transition-colors">
                        شروط الاستخدام
                    </button>
                    <span>|</span>
                    <button id="cookie-policy-btn" class="hover:text-pink-400 transition-colors">
                        سياسة الكوكيز
                    </button>
                </div>
                <p>جميع الحقوق محفوظة © 2026 VelaSweets</p>
            </div>
        </div>
    </footer>

    <!-- Cart Sidebar -->
    <div id="cart-sidebar" class="fixed top-0 left-0 h-full w-96 bg-white dark:bg-gray-800 shadow-2xl transform -translate-x-full transition-transform duration-300 z-50">
        <div class="flex flex-col h-full">
            <!-- Header -->
            <div class="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
                <h3 class="text-xl font-semibold text-gray-900 dark:text-white">السلة</h3>
                <button id="close-cart" class="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors">
                    <i class="ri-close-line text-xl text-gray-600 dark:text-gray-300"></i>
                </button>
            </div>
            
            <!-- Cart Items -->
            <div id="cart-items" class="flex-1 overflow-y-auto p-6">
                <div class="text-center text-gray-500 dark:text-gray-400 py-8">
                    السلة فارغة
                </div>
            </div>
            
            <!-- Footer -->
            <div class="border-t border-gray-200 dark:border-gray-700 p-6">
                <div class="flex items-center justify-between mb-4">
                    <span class="text-lg font-semibold text-gray-900 dark:text-white">المجموع:</span>
                    <span id="cart-total" class="text-xl font-bold text-pink-600 dark:text-pink-400">0 د.ع</span>
                </div>

                <button id="checkout-btn" class="w-full bg-pink-600 hover:bg-pink-700 text-white py-3 rounded-lg font-semibold transition-colors disabled:opacity-50 disabled:cursor-not-allowed" disabled>
                    اطلب الآن
                </button>
            </div>
        </div>
    </div>

    <!-- Favorites Sidebar -->
    <div id="favorites-sidebar" class="fixed top-0 right-0 h-full w-96 bg-white dark:bg-gray-800 shadow-2xl transform translate-x-full transition-transform duration-300 z-50">
        <div class="flex flex-col h-full">
            <!-- Header -->
            <div class="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
                <h3 class="text-xl font-semibold text-gray-900 dark:text-white">المفضلة</h3>
                <button id="close-favorites" class="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors">
                    <i class="ri-close-line text-xl text-gray-600 dark:text-gray-300"></i>
                </button>
            </div>
            
            <!-- Favorites Items -->
            <div id="favorites-items" class="flex-1 overflow-y-auto p-6">
                <div class="text-center text-gray-500 dark:text-gray-400 py-8">
                    لا توجد منتجات مفضلة
                </div>
            </div>
            
            <!-- Footer -->
            <div class="border-t border-gray-200 dark:border-gray-700 p-6">
                <button id="clear-favorites" class="w-full bg-red-600 hover:bg-red-700 text-white py-3 rounded-lg font-semibold transition-colors disabled:opacity-50 disabled:cursor-not-allowed" disabled>
                    مسح الكل
                </button>
            </div>
        </div>
    </div>

    <!-- Product Details Modal -->
    <div id="product-modal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50 flex items-center justify-center p-4">
        <div class="bg-white dark:bg-gray-800 rounded-2xl max-w-4xl w-full max-h-[90vh] overflow-y-auto">
            <!-- Modal Header -->
            <div class="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
                <h3 id="modal-product-name" class="text-2xl font-bold text-gray-900 dark:text-white"></h3>
                <button id="close-product-modal" class="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors">
                    <i class="ri-close-line text-xl text-gray-600 dark:text-gray-300"></i>
                </button>
            </div>

            <!-- Modal Content -->
            <div class="p-6">
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                    <!-- Product Images -->
                    <div class="space-y-4">
                        <!-- Main Image -->
                        <div class="aspect-square bg-gray-100 dark:bg-gray-700 rounded-xl overflow-hidden">
                            <img id="modal-main-image" src="" alt="" class="w-full h-full object-cover">
                        </div>

                        <!-- Image Thumbnails -->
                        <div class="flex gap-2 overflow-x-auto">
                            <div id="modal-thumbnails" class="flex gap-2">
                                <!-- Thumbnails will be populated by JavaScript -->
                            </div>
                        </div>
                    </div>

                    <!-- Product Info -->
                    <div class="space-y-6">
                        <!-- Price and Rating -->
                        <div class="flex items-center justify-between">
                            <div class="text-3xl font-bold text-pink-600 dark:text-pink-400" id="modal-product-price"></div>
                            <div class="flex items-center gap-2">
                                <div class="flex text-yellow-400" id="modal-product-rating">
                                    <i class="ri-star-fill"></i>
                                    <i class="ri-star-fill"></i>
                                    <i class="ri-star-fill"></i>
                                    <i class="ri-star-fill"></i>
                                    <i class="ri-star-line"></i>
                                </div>
                                <span class="text-sm text-gray-600 dark:text-gray-400">(4.2)</span>
                            </div>
                        </div>

                        <!-- Description -->
                        <div>
                            <h4 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">الوصف</h4>
                            <p id="modal-product-description" class="text-gray-600 dark:text-gray-300 leading-relaxed"></p>
                        </div>

                        <!-- Options -->
                        <div id="modal-product-options" class="space-y-4">
                            <!-- Options will be populated by JavaScript -->
                        </div>

                        <!-- Quantity Selector -->
                        <div>
                            <h4 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">الكمية</h4>
                            <div class="flex items-center gap-3">
                                <button id="decrease-quantity" class="w-10 h-10 bg-gray-200 dark:bg-gray-600 rounded-lg flex items-center justify-center hover:bg-gray-300 dark:hover:bg-gray-500 transition-colors">
                                    <i class="ri-subtract-line"></i>
                                </button>
                                <span id="modal-quantity" class="text-xl font-semibold min-w-[3rem] text-center">1</span>
                                <button id="increase-quantity" class="w-10 h-10 bg-gray-200 dark:bg-gray-600 rounded-lg flex items-center justify-center hover:bg-gray-300 dark:hover:bg-gray-500 transition-colors">
                                    <i class="ri-add-line"></i>
                                </button>
                            </div>
                        </div>

                        <!-- Action Buttons -->
                        <div class="flex gap-4">
                            <button id="modal-add-to-cart" class="flex-1 bg-pink-600 hover:bg-pink-700 text-white px-6 py-3 rounded-lg font-semibold transition-colors flex items-center justify-center gap-2">
                                <i class="ri-shopping-cart-line"></i>
                                أضف للسلة
                            </button>
                            <button id="modal-add-to-favorites" class="p-3 border-2 border-pink-600 text-pink-600 hover:bg-pink-600 hover:text-white rounded-lg transition-colors">
                                <i class="ri-heart-line"></i>
                            </button>
                        </div>

                        <!-- Product Details -->
                        <div class="border-t border-gray-200 dark:border-gray-700 pt-6">
                            <h4 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">تفاصيل إضافية</h4>
                            <div class="space-y-2 text-sm">
                                <div class="flex justify-between">
                                    <span class="text-gray-600 dark:text-gray-400">مدة الصلاحية:</span>
                                    <span class="text-gray-900 dark:text-white">3-5 أيام</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-600 dark:text-gray-400">طريقة الحفظ:</span>
                                    <span class="text-gray-900 dark:text-white">مكان بارد وجاف</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-600 dark:text-gray-400">التوصيل:</span>
                                    <span class="text-gray-900 dark:text-white">نفس اليوم في البصرة</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Reviews Section -->
                <div class="mt-8 border-t border-gray-200 dark:border-gray-700 pt-8">
                    <h4 class="text-xl font-semibold text-gray-900 dark:text-white mb-6">التقييمات والمراجعات</h4>

                    <!-- Add Review -->
                    <div class="bg-gray-50 dark:bg-gray-700 rounded-xl p-6 mb-6">
                        <h5 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">أضف تقييمك</h5>
                        <div class="space-y-4">
                            <!-- Star Rating -->
                            <div class="flex items-center gap-2">
                                <span class="text-sm text-gray-600 dark:text-gray-400">التقييم:</span>
                                <div class="flex gap-1" id="rating-stars">
                                    <i class="ri-star-line text-2xl text-gray-300 cursor-pointer hover:text-yellow-400 transition-colors" data-rating="1"></i>
                                    <i class="ri-star-line text-2xl text-gray-300 cursor-pointer hover:text-yellow-400 transition-colors" data-rating="2"></i>
                                    <i class="ri-star-line text-2xl text-gray-300 cursor-pointer hover:text-yellow-400 transition-colors" data-rating="3"></i>
                                    <i class="ri-star-line text-2xl text-gray-300 cursor-pointer hover:text-yellow-400 transition-colors" data-rating="4"></i>
                                    <i class="ri-star-line text-2xl text-gray-300 cursor-pointer hover:text-yellow-400 transition-colors" data-rating="5"></i>
                                </div>
                            </div>

                            <!-- Review Text -->
                            <textarea id="review-text" placeholder="اكتب مراجعتك هنا..." class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent bg-white dark:bg-gray-600 text-gray-900 dark:text-white resize-none" rows="3"></textarea>

                            <!-- Submit Review -->
                            <button id="submit-review" class="bg-pink-600 hover:bg-pink-700 text-white px-6 py-2 rounded-lg font-semibold transition-colors">
                                إرسال التقييم
                            </button>
                        </div>
                    </div>

                    <!-- Reviews List -->
                    <div id="reviews-list" class="space-y-4">
                        <!-- Sample Review -->
                        <div class="bg-white dark:bg-gray-700 rounded-xl p-6 border border-gray-200 dark:border-gray-600">
                            <div class="flex items-center justify-between mb-3">
                                <div class="flex items-center gap-3">
                                    <div class="w-10 h-10 bg-pink-100 dark:bg-pink-900 rounded-full flex items-center justify-center">
                                        <i class="ri-user-line text-pink-600 dark:text-pink-400"></i>
                                    </div>
                                    <div>
                                        <h6 class="font-semibold text-gray-900 dark:text-white">أحمد محمد</h6>
                                        <div class="flex text-yellow-400 text-sm">
                                            <i class="ri-star-fill"></i>
                                            <i class="ri-star-fill"></i>
                                            <i class="ri-star-fill"></i>
                                            <i class="ri-star-fill"></i>
                                            <i class="ri-star-fill"></i>
                                        </div>
                                    </div>
                                </div>
                                <span class="text-sm text-gray-500 dark:text-gray-400">منذ يومين</span>
                            </div>
                            <p class="text-gray-600 dark:text-gray-300">منتج رائع وطعم مميز، التوصيل كان سريع والتغليف ممتاز. أنصح بالتجربة!</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Overlay -->
    <div id="overlay" class="fixed inset-0 bg-black bg-opacity-50 hidden z-40"></div>

    <!-- Analytics Modal -->
    <div id="analytics-modal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50 flex items-center justify-center p-4">
        <div class="bg-white dark:bg-gray-800 rounded-2xl max-w-6xl w-full max-h-[90vh] overflow-y-auto">
            <!-- Modal Header -->
            <div class="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
                <h3 class="text-2xl font-bold text-gray-900 dark:text-white">إحصائيات المتجر</h3>
                <button id="close-analytics-modal" class="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors">
                    <i class="ri-close-line text-xl text-gray-600 dark:text-gray-300"></i>
                </button>
            </div>

            <!-- Modal Content -->
            <div class="p-6">
                <!-- Stats Cards -->
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                    <!-- Total Orders -->
                    <div class="bg-gradient-to-r from-blue-500 to-blue-600 rounded-xl p-6 text-white">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-blue-100 text-sm">إجمالي الطلبات</p>
                                <p id="total-orders" class="text-3xl font-bold">0</p>
                            </div>
                            <i class="ri-shopping-bag-line text-4xl text-blue-200"></i>
                        </div>
                    </div>

                    <!-- Total Revenue -->
                    <div class="bg-gradient-to-r from-green-500 to-green-600 rounded-xl p-6 text-white">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-green-100 text-sm">إجمالي المبيعات</p>
                                <p id="total-revenue" class="text-3xl font-bold">0 د.ع</p>
                            </div>
                            <i class="ri-money-dollar-circle-line text-4xl text-green-200"></i>
                        </div>
                    </div>

                    <!-- Total Products -->
                    <div class="bg-gradient-to-r from-purple-500 to-purple-600 rounded-xl p-6 text-white">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-purple-100 text-sm">عدد المنتجات</p>
                                <p id="total-products" class="text-3xl font-bold">3</p>
                            </div>
                            <i class="ri-gift-line text-4xl text-purple-200"></i>
                        </div>
                    </div>

                    <!-- Average Rating -->
                    <div class="bg-gradient-to-r from-yellow-500 to-yellow-600 rounded-xl p-6 text-white">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-yellow-100 text-sm">متوسط التقييم</p>
                                <p id="average-rating" class="text-3xl font-bold">0.0</p>
                            </div>
                            <i class="ri-star-line text-4xl text-yellow-200"></i>
                        </div>
                    </div>
                </div>

                <!-- Charts Section -->
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                    <!-- Best Selling Products -->
                    <div class="bg-gray-50 dark:bg-gray-700 rounded-xl p-6">
                        <h4 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">المنتجات الأكثر مبيعاً</h4>
                        <div id="best-selling-products" class="space-y-4">
                            <!-- Products will be populated by JavaScript -->
                        </div>
                    </div>

                    <!-- Recent Activity -->
                    <div class="bg-gray-50 dark:bg-gray-700 rounded-xl p-6">
                        <h4 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">النشاط الأخير</h4>
                        <div id="recent-activity" class="space-y-3">
                            <!-- Activity will be populated by JavaScript -->
                        </div>
                    </div>
                </div>

                <!-- Product Performance -->
                <div class="mt-8 bg-gray-50 dark:bg-gray-700 rounded-xl p-6">
                    <h4 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">أداء المنتجات</h4>
                    <div class="overflow-x-auto">
                        <table class="w-full text-sm">
                            <thead>
                                <tr class="border-b border-gray-200 dark:border-gray-600">
                                    <th class="text-right py-3 text-gray-600 dark:text-gray-300">المنتج</th>
                                    <th class="text-right py-3 text-gray-600 dark:text-gray-300">المبيعات</th>
                                    <th class="text-right py-3 text-gray-600 dark:text-gray-300">الإيرادات</th>
                                    <th class="text-right py-3 text-gray-600 dark:text-gray-300">التقييم</th>
                                    <th class="text-right py-3 text-gray-600 dark:text-gray-300">المفضلة</th>
                                </tr>
                            </thead>
                            <tbody id="product-performance-table">
                                <!-- Table rows will be populated by JavaScript -->
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- Export Options -->
                <div class="mt-8 flex justify-end gap-4">
                    <button id="export-csv" class="px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded-lg font-medium transition-colors">
                        تصدير CSV
                    </button>
                    <button id="print-analytics" class="px-4 py-2 bg-pink-600 hover:bg-pink-700 text-white rounded-lg font-medium transition-colors">
                        طباعة التقرير
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Checkout Modal -->
    <div id="checkout-modal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50 flex items-center justify-center p-4">
        <div class="bg-white dark:bg-gray-800 rounded-2xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
            <!-- Modal Header -->
            <div class="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
                <h3 class="text-2xl font-bold text-gray-900 dark:text-white">إتمام الطلب</h3>
                <button id="close-checkout-modal" class="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors">
                    <i class="ri-close-line text-xl text-gray-600 dark:text-gray-300"></i>
                </button>
            </div>

            <!-- Modal Content -->
            <div class="p-6">
                <!-- Order Summary -->
                <div class="mb-8">
                    <h4 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">ملخص الطلب</h4>
                    <div id="checkout-items" class="space-y-3 mb-4">
                        <!-- Items will be populated by JavaScript -->
                    </div>
                    <div class="border-t border-gray-200 dark:border-gray-700 pt-4 space-y-3">
                        <div class="flex justify-between items-center">
                            <span class="text-gray-700 dark:text-gray-300">مجموع المنتجات:</span>
                            <span id="checkout-subtotal" class="text-gray-900 dark:text-white">0 د.ع</span>
                        </div>
                        <div class="flex justify-between items-center">
                            <span class="text-gray-700 dark:text-gray-300">رسوم التوصيل:</span>
                            <span id="checkout-delivery" class="text-gray-900 dark:text-white">يُحدد حسب المحافظة</span>
                        </div>
                        <div class="border-t border-gray-200 dark:border-gray-700 pt-3">
                            <div class="flex justify-between items-center text-lg font-bold">
                                <span class="text-gray-900 dark:text-white">المجموع النهائي:</span>
                                <span id="checkout-total" class="text-pink-600 dark:text-pink-400">0 د.ع</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Customer Information -->
                <form id="checkout-form" class="space-y-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">الاسم الكامل *</label>
                            <input type="text" id="customer-name" required class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">رقم الهاتف *</label>
                            <input type="tel" id="customer-phone" required class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white">
                        </div>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">العنوان *</label>
                        <textarea id="customer-address" required rows="3" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white" placeholder="العنوان التفصيلي للتوصيل"></textarea>
                    </div>

                    <!-- Payment Method -->
                    <div>
                        <h4 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">طريقة الدفع</h4>
                        <div class="bg-green-50 dark:bg-green-900/20 border-2 border-green-200 dark:border-green-800 rounded-lg p-4">
                            <div class="flex items-center">
                                <input type="radio" name="payment-method" value="cash" checked disabled class="text-green-600 focus:ring-green-500">
                                <div class="mr-3">
                                    <i class="ri-money-dollar-circle-line text-3xl text-green-600"></i>
                                </div>
                                <div>
                                    <div class="font-semibold text-green-800 dark:text-green-200">الدفع عند التسليم</div>
                                    <div class="text-sm text-green-700 dark:text-green-300">ادفع نقداً عند استلام الطلب - الطريقة الوحيدة المتاحة</div>
                                </div>
                            </div>
                        </div>
                    </div>



                    <!-- Order Notes -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">ملاحظات إضافية</label>
                        <textarea id="order-notes" rows="3" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white" placeholder="أي ملاحظات خاصة بالطلب..."></textarea>
                    </div>

                    <!-- Submit Button -->
                    <button type="submit" id="place-order-btn" class="w-full bg-pink-600 hover:bg-pink-700 text-white py-3 rounded-lg font-semibold transition-colors flex items-center justify-center gap-2">
                        <i class="ri-shopping-bag-line"></i>
                        تأكيد الطلب
                    </button>
                </form>
            </div>
        </div>
    </div>

    <!-- Profile Modal -->
    <div id="profile-modal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50 flex items-center justify-center p-4">
        <div class="bg-white dark:bg-gray-800 rounded-2xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
            <!-- Modal Header -->
            <div class="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
                <h3 class="text-2xl font-bold text-gray-900 dark:text-white">الملف الشخصي</h3>
                <button id="close-profile-modal" class="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors">
                    <i class="ri-close-line text-xl text-gray-600 dark:text-gray-300"></i>
                </button>
            </div>

            <!-- Modal Content -->
            <div class="p-6">
                <!-- Profile Selection -->
                <div class="mb-6">
                    <div class="flex items-center justify-between mb-4">
                        <h4 class="text-lg font-semibold text-gray-900 dark:text-white">الملفات الشخصية</h4>
                        <button id="add-profile-btn" class="bg-pink-600 hover:bg-pink-700 text-white px-4 py-2 rounded-lg font-medium transition-colors flex items-center gap-2">
                            <i class="ri-add-line"></i>
                            إضافة ملف جديد
                        </button>
                    </div>

                    <div id="profiles-list" class="space-y-3">
                        <!-- Profiles will be populated by JavaScript -->
                    </div>
                </div>

                <!-- Profile Form -->
                <form id="profile-form" class="space-y-6">
                    <input type="hidden" id="profile-id" value="">

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">الاسم الكامل *</label>
                            <input type="text" id="profile-name" required class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">نوع الملف</label>
                            <select id="profile-type" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white">
                                <option value="home">المنزل</option>
                                <option value="work">العمل</option>
                                <option value="other">أخرى</option>
                            </select>
                        </div>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">رقم الهاتف الأساسي *</label>
                            <input type="tel" id="profile-phone1" required maxlength="11" pattern="07[0-9]{9}" placeholder="07XXXXXXXXX" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white">
                            <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">يجب أن يبدأ بـ 07 ويحتوي على 11 رقم</p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">رقم الهاتف الاحتياطي</label>
                            <input type="tel" id="profile-phone2" maxlength="11" pattern="07[0-9]{9}" placeholder="07XXXXXXXXX" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white">
                        </div>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">المحافظة *</label>
                        <select id="profile-governorate" required class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white">
                            <option value="">اختر المحافظة</option>
                            <option value="baghdad">بغداد</option>
                            <option value="basra">البصرة</option>
                            <option value="mosul">الموصل</option>
                            <option value="erbil">أربيل</option>
                            <option value="najaf">النجف</option>
                            <option value="karbala">كربلاء</option>
                            <option value="hillah">الحلة</option>
                            <option value="ramadi">الرمادي</option>
                            <option value="tikrit">تكريت</option>
                            <option value="kirkuk">كركوك</option>
                            <option value="sulaymaniyah">السليمانية</option>
                            <option value="duhok">دهوك</option>
                            <option value="kut">الكوت</option>
                            <option value="amarah">العمارة</option>
                            <option value="nasiriyah">الناصرية</option>
                            <option value="diwaniyah">الديوانية</option>
                            <option value="samawah">السماوة</option>
                            <option value="fallujah">الفلوجة</option>
                        </select>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">العنوان التفصيلي *</label>
                        <textarea id="profile-address" required rows="3" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white" placeholder="المنطقة، الحي، الشارع، رقم البيت..."></textarea>
                    </div>

                    <!-- Form Actions -->
                    <div class="flex gap-4">
                        <button type="submit" class="flex-1 bg-pink-600 hover:bg-pink-700 text-white py-3 rounded-lg font-semibold transition-colors">
                            حفظ الملف الشخصي
                        </button>
                        <button type="button" id="delete-profile-btn" class="px-6 py-3 bg-red-600 hover:bg-red-700 text-white rounded-lg font-semibold transition-colors hidden">
                            حذف
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Orders Tracking Modal -->
    <div id="orders-modal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50 flex items-center justify-center p-4">
        <div class="bg-white dark:bg-gray-800 rounded-2xl max-w-4xl w-full max-h-[90vh] overflow-y-auto">
            <!-- Modal Header -->
            <div class="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
                <h3 class="text-2xl font-bold text-gray-900 dark:text-white">تتبع الطلبات</h3>
                <button id="close-orders-modal" class="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors">
                    <i class="ri-close-line text-xl text-gray-600 dark:text-gray-300"></i>
                </button>
            </div>

            <!-- Modal Content -->
            <div class="p-6">
                <div id="orders-list" class="space-y-6">
                    <!-- Orders will be populated by JavaScript -->
                </div>

                <div id="no-orders" class="text-center py-12 hidden">
                    <i class="ri-shopping-bag-line text-6xl text-gray-400 mb-4"></i>
                    <h4 class="text-xl font-semibold text-gray-600 dark:text-gray-400 mb-2">لا توجد طلبات</h4>
                    <p class="text-gray-500 dark:text-gray-500">لم تقم بإجراء أي طلبات بعد</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Mobile Profile Menu Modal -->
    <div id="mobile-profile-modal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50 flex items-end justify-center md:hidden">
        <div class="bg-white dark:bg-gray-800 rounded-t-2xl w-full max-w-md">
            <!-- Modal Header -->
            <div class="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
                <h3 class="text-xl font-bold text-gray-900 dark:text-white">الحساب</h3>
                <button id="close-mobile-profile-modal" class="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors">
                    <i class="ri-close-line text-xl text-gray-600 dark:text-gray-300"></i>
                </button>
            </div>

            <!-- Modal Content -->
            <div class="p-6 space-y-4">
                <button id="mobile-profile-settings-btn" class="w-full text-right p-4 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors rounded-lg flex items-center gap-4">
                    <i class="ri-user-line text-2xl text-pink-600 dark:text-pink-400"></i>
                    <div>
                        <p class="font-semibold text-gray-900 dark:text-white">الملف الشخصي</p>
                        <p class="text-sm text-gray-500 dark:text-gray-400">إدارة معلوماتك الشخصية</p>
                    </div>
                </button>

                <button id="mobile-orders-tracking-btn" class="w-full text-right p-4 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors rounded-lg flex items-center gap-4">
                    <i class="ri-truck-line text-2xl text-pink-600 dark:text-pink-400"></i>
                    <div>
                        <p class="font-semibold text-gray-900 dark:text-white">تتبع الطلبات</p>
                        <p class="text-sm text-gray-500 dark:text-gray-400">تابع حالة طلباتك</p>
                    </div>
                </button>
            </div>
        </div>
    </div>

    <!-- Privacy Policy Modal -->
    <div id="privacy-modal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50 flex items-center justify-center p-4">
        <div class="bg-white dark:bg-gray-800 rounded-2xl max-w-4xl w-full max-h-[90vh] overflow-y-auto">
            <!-- Modal Header -->
            <div class="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
                <h3 id="privacy-modal-title" class="text-2xl font-bold text-gray-900 dark:text-white">سياسة الخصوصية</h3>
                <button id="close-privacy-modal" class="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors">
                    <i class="ri-close-line text-xl text-gray-600 dark:text-gray-300"></i>
                </button>
            </div>

            <!-- Modal Content -->
            <div id="privacy-modal-content" class="p-6 prose prose-gray dark:prose-invert max-w-none">
                <!-- Content will be populated by JavaScript -->
            </div>
        </div>
    </div>

    <!-- Mobile Navigation -->
    <nav class="mobile-nav md:hidden">
        <div class="flex justify-around items-center">
            <button onclick="scrollToSection('home')" class="flex flex-col items-center p-2 text-gray-600 dark:text-gray-400 hover:text-pink-600 dark:hover:text-pink-400 transition-colors">
                <i class="ri-home-line text-xl"></i>
                <span class="text-xs mt-1">الرئيسية</span>
            </button>
            <button onclick="scrollToSection('products')" class="flex flex-col items-center p-2 text-gray-600 dark:text-gray-400 hover:text-pink-600 dark:hover:text-pink-400 transition-colors">
                <i class="ri-gift-line text-xl"></i>
                <span class="text-xs mt-1">المنتجات</span>
            </button>
            <button id="mobile-cart-btn" class="flex flex-col items-center p-2 text-gray-600 dark:text-gray-400 hover:text-pink-600 dark:hover:text-pink-400 transition-colors relative">
                <i class="ri-shopping-cart-line text-xl"></i>
                <span class="text-xs mt-1">السلة</span>
                <span id="mobile-cart-count" class="absolute -top-1 -right-1 bg-red-500/90 text-white text-xs font-medium rounded-lg px-1.5 py-0.5 shadow-sm border border-red-400/50 hidden">0</span>
            </button>
            <button id="mobile-favorites-btn" class="flex flex-col items-center p-2 text-gray-600 dark:text-gray-400 hover:text-pink-600 dark:hover:text-pink-400 transition-colors relative">
                <i class="ri-heart-line text-xl"></i>
                <span class="text-xs mt-1">المفضلة</span>
                <span id="mobile-favorites-count" class="absolute -top-1 -right-1 bg-red-500/90 text-white text-xs font-medium rounded-lg px-1.5 py-0.5 shadow-sm border border-red-400/50 hidden">0</span>
            </button>
            <button id="mobile-profile-btn" class="flex flex-col items-center p-2 text-gray-600 dark:text-gray-400 hover:text-pink-600 dark:hover:text-pink-400 transition-colors">
                <i class="ri-user-line text-xl"></i>
                <span class="text-xs mt-1">الحساب</span>
            </button>
        </div>
    </nav>



    <!-- Custom Alert Modal -->
    <div id="custom-alert" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-[9999] hidden">
        <div class="bg-white dark:bg-gray-800 rounded-2xl shadow-2xl max-w-sm w-full mx-4 transform scale-95 transition-all duration-300">
            <div class="p-6 text-center">
                <div id="alert-icon" class="w-16 h-16 mx-auto mb-4 rounded-full flex items-center justify-center">
                    <i id="alert-icon-class" class="text-3xl"></i>
                </div>
                <h3 id="alert-title" class="text-lg font-semibold text-gray-900 dark:text-white mb-2"></h3>
                <p id="alert-message" class="text-gray-600 dark:text-gray-400 mb-6"></p>
                <button id="alert-ok" class="w-full bg-pink-600 hover:bg-pink-700 text-white py-3 px-6 rounded-xl font-medium transition-colors">
                    موافق
                </button>
            </div>
        </div>
    </div>

    <!-- Custom Confirm Modal -->
    <div id="custom-confirm" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-[9999] hidden">
        <div class="bg-white dark:bg-gray-800 rounded-2xl shadow-2xl max-w-sm w-full mx-4 transform scale-95 transition-all duration-300">
            <div class="p-6 text-center">
                <div class="w-16 h-16 mx-auto mb-4 bg-yellow-100 dark:bg-yellow-900/20 rounded-full flex items-center justify-center">
                    <i class="ri-question-line text-3xl text-yellow-600 dark:text-yellow-400"></i>
                </div>
                <h3 id="confirm-title" class="text-lg font-semibold text-gray-900 dark:text-white mb-2">تأكيد العملية</h3>
                <p id="confirm-message" class="text-gray-600 dark:text-gray-400 mb-6"></p>
                <div class="flex gap-3">
                    <button id="confirm-cancel" class="flex-1 bg-gray-200 dark:bg-gray-700 hover:bg-gray-300 dark:hover:bg-gray-600 text-gray-800 dark:text-gray-200 py-3 px-6 rounded-xl font-medium transition-colors">
                        إلغاء
                    </button>
                    <button id="confirm-ok" class="flex-1 bg-pink-600 hover:bg-pink-700 text-white py-3 px-6 rounded-xl font-medium transition-colors">
                        تأكيد
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Custom Prompt Modal -->
    <div id="custom-prompt" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-[9999] hidden">
        <div class="bg-white dark:bg-gray-800 rounded-2xl shadow-2xl max-w-sm w-full mx-4 transform scale-95 transition-all duration-300">
            <div class="p-6">
                <div class="w-16 h-16 mx-auto mb-4 bg-pink-100 dark:bg-pink-900/20 rounded-full flex items-center justify-center">
                    <i class="ri-edit-line text-3xl text-pink-600 dark:text-pink-400"></i>
                </div>
                <h3 id="prompt-title" class="text-lg font-semibold text-gray-900 dark:text-white mb-2 text-center">إدخال البيانات</h3>
                <p id="prompt-message" class="text-gray-600 dark:text-gray-400 mb-4 text-center"></p>
                <input id="prompt-input" type="text" class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-pink-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white mb-6" placeholder="أدخل النص هنا...">
                <div class="flex gap-3">
                    <button id="prompt-cancel" class="flex-1 bg-gray-200 dark:bg-gray-700 hover:bg-gray-300 dark:hover:bg-gray-600 text-gray-800 dark:text-gray-200 py-3 px-6 rounded-xl font-medium transition-colors">
                        إلغاء
                    </button>
                    <button id="prompt-ok" class="flex-1 bg-pink-600 hover:bg-pink-700 text-white py-3 px-6 rounded-xl font-medium transition-colors">
                        موافق
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="script.js"></script>
</body>
</html>

/* Custom CSS for VelaSweets */

/* Font Family */
.font-cairo {
    font-family: 'Cairo', sans-serif;
}

/* Custom Scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
    background: #e91e63;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #c2185b;
}

/* Dark mode scrollbar */
.dark ::-webkit-scrollbar-track {
    background: #374151;
}

.dark ::-webkit-scrollbar-thumb {
    background: #ec4899;
}

.dark ::-webkit-scrollbar-thumb:hover {
    background: #db2777;
}

/* Smooth scrolling */
html {
    scroll-behavior: smooth;
}

/* Custom animations */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(100px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-100px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes bounce {
    0%, 20%, 53%, 80%, 100% {
        transform: translate3d(0,0,0);
    }
    40%, 43% {
        transform: translate3d(0, -30px, 0);
    }
    70% {
        transform: translate3d(0, -15px, 0);
    }
    90% {
        transform: translate3d(0, -4px, 0);
    }
}

/* Animation classes */
.animate-fade-in {
    animation: fadeIn 0.6s ease-out;
}

.animate-slide-in-right {
    animation: slideInRight 0.6s ease-out;
}

.animate-slide-in-left {
    animation: slideInLeft 0.6s ease-out;
}

.animate-bounce-custom {
    animation: bounce 2s infinite;
}

/* Custom button styles */
.btn-primary {
    background-color: #ec4899;
    color: white;
    padding: 0.75rem 1.5rem;
    border-radius: 0.5rem;
    font-weight: 600;
    transition: all 0.3s ease;
    transform: scale(1);
}

.btn-primary:hover {
    background-color: #be185d;
    transform: scale(1.05);
}

.btn-secondary {
    border: 2px solid #ec4899;
    color: #ec4899;
    background-color: transparent;
    padding: 0.75rem 1.5rem;
    border-radius: 0.5rem;
    font-weight: 600;
    transition: all 0.3s ease;
    transform: scale(1);
}

.btn-secondary:hover {
    background-color: #ec4899;
    color: white;
    transform: scale(1.05);
}

/* Product Button Enhancements */
.product-card .btn-primary,
.product-card .btn-secondary {
    font-size: 0.875rem;
    padding: 0.5rem 1rem;
    border-radius: 0.5rem;
    font-weight: 600;
    transition: all 0.2s ease;
}

.product-card .btn-primary:hover,
.product-card .btn-secondary:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(236, 72, 153, 0.3);
}

/* Custom gradient backgrounds */
.gradient-pink {
    background: linear-gradient(135deg, #ec4899 0%, #be185d 100%);
}

/* Loading spinner */
.spinner {
    border: 4px solid #f3f3f3;
    border-top: 4px solid #ec4899;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Simple success animation */
.success-icon {
    color: #10b981;
    animation: bounce 0.6s ease-in-out;
}

@keyframes bounce {
    0%, 20%, 60%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-10px);
    }
    80% {
        transform: translateY(-5px);
    }
}

/* Custom form styles */
.form-input {
    width: 100%;
    padding: 0.75rem 1rem;
    border: 1px solid #d1d5db;
    border-radius: 0.5rem;
    background-color: white;
    color: #111827;
    transition: all 0.3s ease;
}

.form-input:focus {
    outline: none;
    border-color: #ec4899;
    transform: translateY(-2px);
    box-shadow: 0 0 0 2px rgba(236, 72, 153, 0.2), 0 4px 12px rgba(236, 72, 153, 0.15);
}

/* Dark mode form inputs */
.dark .form-input {
    border-color: #4b5563;
    background-color: #374151;
    color: white;
}

/* Custom card styles */
.card {
    background-color: white;
    border-radius: 0.75rem;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    padding: 1.5rem;
    transition: all 0.3s ease;
}

.card:hover {
    transform: translateY(-4px);
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
}

/* Dark mode cards */
.dark .card {
    background-color: #1f2937;
}

/* Product Grid Enhancements */
.product-grid {
    display: grid;
    gap: 2rem;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
}

@media (max-width: 768px) {
    .product-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }
}

/* Product Card Enhancements */
.product-card {
    background: white;
    border-radius: 1rem;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    transition: all 0.3s ease;
    transform: translateY(0);
}

.product-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

.dark .product-card {
    background: #1f2937;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.3);
}

.dark .product-card:hover {
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.4), 0 10px 10px -5px rgba(0, 0, 0, 0.2);
}

/* Product Image Enhancements */
.product-image {
    width: 100%;
    height: 16rem;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.product-card:hover .product-image {
    transform: scale(1.05);
}

/* Custom spacing for Arabic text */
.arabic-text {
    line-height: 1.8;
    letter-spacing: 0.5px;
}

/* Price styling */
.price {
    font-size: 1.5rem;
    font-weight: bold;
    color: #ec4899;
    font-feature-settings: 'tnum';
}

.dark .price {
    color: #f9a8d4;
}

/* Responsive utilities */
@media (max-width: 640px) {
    .container {
        padding-left: 1rem;
        padding-right: 1rem;
    }
}

/* Print styles */
@media print {
    .no-print {
        display: none !important;
    }
    
    body {
        background: white !important;
        color: black !important;
    }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .btn-primary {
        border: 2px solid currentColor;
    }
    
    .card {
        border: 1px solid currentColor;
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* Focus styles for accessibility */
.focus-visible:focus {
    outline: 2px solid #ec4899;
    outline-offset: 2px;
}

/* Custom utilities */
.text-shadow {
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.backdrop-blur-custom {
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
}

/* Loading states */
.loading {
    position: relative;
    overflow: hidden;
}

/* Advanced Loading Animations */
.spinner {
    width: 20px;
    height: 20px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #ec4899;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

.spinner-large {
    width: 40px;
    height: 40px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #ec4899;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Skeleton Loading */
.skeleton {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
    border-radius: 8px;
}

.dark .skeleton {
    background: linear-gradient(90deg, #374151 25%, #4b5563 50%, #374151 75%);
    background-size: 200% 100%;
}

@keyframes loading {
    0% { background-position: 200% 0; }
    100% { background-position: -200% 0; }
}

.skeleton-text {
    height: 1rem;
    border-radius: 4px;
    margin-bottom: 0.5rem;
}

.skeleton-text:last-child {
    width: 60%;
}

.skeleton-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
}

.skeleton-button {
    height: 2.5rem;
    border-radius: 8px;
}

.skeleton-image {
    width: 100%;
    border-radius: 8px;
}

.skeleton-card {
    background: white;
    border-radius: 1rem;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.dark .skeleton-card {
    background: #1f2937;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.3);
}

/* Image loading states */
.product-image {
    transition: opacity 0.3s ease;
}

.product-image.loading {
    opacity: 0.7;
}

.product-image.loaded {
    opacity: 1;
}

.product-image.error {
    opacity: 0.5;
    filter: grayscale(100%);
}

/* Pulse Animation */
.pulse {
    animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

/* Bounce Animation */
.bounce-in {
    animation: bounceIn 0.6s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

@keyframes bounceIn {
    0% {
        transform: scale(0.3);
        opacity: 0;
    }
    50% {
        transform: scale(1.05);
    }
    70% {
        transform: scale(0.9);
    }
    100% {
        transform: scale(1);
        opacity: 1;
    }
}

/* Slide Animations */
.slide-in-right {
    animation: slideInRight 0.5s ease-out;
}

.slide-in-left {
    animation: slideInLeft 0.5s ease-out;
}

.slide-in-up {
    animation: slideInUp 0.5s ease-out;
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes slideInLeft {
    from {
        transform: translateX(-100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes slideInUp {
    from {
        transform: translateY(100%);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

/* Scale Animations */
.scale-in {
    animation: scaleIn 0.3s ease-out;
}

@keyframes scaleIn {
    from {
        transform: scale(0.9);
        opacity: 0;
    }
    to {
        transform: scale(1);
        opacity: 1;
    }
}

/* Shake Animation */
.shake {
    animation: shake 0.5s ease-in-out;
}

@keyframes shake {
    0%, 100% { transform: translateX(0); }
    10%, 30%, 50%, 70%, 90% { transform: translateX(-5px); }
    20%, 40%, 60%, 80% { transform: translateX(5px); }
}

/* Progress Bar */
.progress-bar {
    width: 100%;
    height: 4px;
    background-color: #e5e7eb;
    border-radius: 2px;
    overflow: hidden;
}

.progress-bar-fill {
    height: 100%;
    background: linear-gradient(90deg, #ec4899, #be185d);
    border-radius: 2px;
    transition: width 0.3s ease;
}

.progress-bar-animated {
    background: linear-gradient(90deg, #ec4899, #be185d, #ec4899);
    background-size: 200% 100%;
    animation: progressMove 2s linear infinite;
}

@keyframes progressMove {
    0% { background-position: 200% 0; }
    100% { background-position: -200% 0; }
}

/* Button Loading State */
.btn-loading {
    position: relative;
    color: transparent !important;
}

.btn-loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 20px;
    height: 20px;
    border: 2px solid transparent;
    border-top: 2px solid currentColor;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    color: white;
}



/* Ripple Effect */
.ripple {
    position: relative;
    overflow: hidden;
}

.ripple::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.3);
    transform: translate(-50%, -50%);
    transition: width 0.6s, height 0.6s;
}

.ripple:active::before {
    width: 300px;
    height: 300px;
}

/* Lazy Loading Images */
img.lazy {
    opacity: 0;
    transition: opacity 0.3s;
}

img.loaded {
    opacity: 1;
}

img.lazy::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
}

.dark img.lazy::before {
    background: linear-gradient(90deg, #374151 25%, #4b5563 50%, #374151 75%);
    background-size: 200% 100%;
}

/* Performance Optimizations */
* {
    box-sizing: border-box;
}

img {
    max-width: 100%;
    height: auto;
}

/* Reduce motion for users who prefer it */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
    }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .btn-primary,
    .btn-secondary {
        border: 2px solid currentColor;
    }

    .card {
        border: 1px solid currentColor;
    }

    .notification {
        border: 2px solid currentColor;
    }
}

/* Print styles */
@media print {
    .no-print {
        display: none !important;
    }

    #overlay,
    .notification {
        display: none !important;
    }

    body {
        background: white !important;
        color: black !important;
    }

    .bg-gray-800,
    .bg-gray-900 {
        background: white !important;
        color: black !important;
    }

    .text-white {
        color: black !important;
    }
}

/* Security enhancements */
.secure-input {
    position: relative;
}

.secure-input::after {
    content: '🔒';
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    font-size: 12px;
    opacity: 0.5;
}

/* Error states */
.error-state {
    border-color: #ef4444 !important;
    background-color: #fef2f2;
}

.dark .error-state {
    background-color: #7f1d1d;
}

.error-message {
    color: #ef4444;
    font-size: 0.875rem;
    margin-top: 0.25rem;
}

/* Success states */
.success-state {
    border-color: #10b981 !important;
    background-color: #f0fdf4;
}

.dark .success-state {
    background-color: #064e3b;
}

.success-message {
    color: #10b981;
    font-size: 0.875rem;
    margin-top: 0.25rem;
}

/* Mobile Optimizations */
@media (max-width: 768px) {
    /* Touch-friendly buttons */
    button, .btn {
        min-height: 44px;
        min-width: 44px;
        padding: 12px 16px;
    }

    /* Larger tap targets */
    .cart-item button,
    .favorite-item button {
        min-height: 40px;
        min-width: 40px;
    }

    /* Mobile-friendly modals */
    .modal-content {
        margin: 10px;
        max-height: calc(100vh - 20px);
        border-radius: 12px;
    }

    /* Improved sidebar width */
    #cart-sidebar,
    #favorites-sidebar {
        width: 100vw;
        max-width: 400px;
    }

    /* Better spacing for mobile */
    .container {
        padding-left: 16px;
        padding-right: 16px;
    }

    /* Mobile navigation */
    .mobile-nav {
        position: fixed;
        bottom: 0;
        left: 0;
        right: 0;
        background: white;
        border-top: 1px solid #e5e7eb;
        padding: 8px 0;
        z-index: 50;
    }

    .dark .mobile-nav {
        background: #1f2937;
        border-top-color: #374151;
    }

    /* Mobile product grid */
    .product-grid {
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        gap: 12px;
    }

    /* Mobile hero text */
    .hero-title {
        font-size: 2.5rem;
        line-height: 1.2;
    }

    /* Mobile form inputs */
    input, textarea, select {
        font-size: 16px; /* Prevents zoom on iOS */
        padding: 12px;
    }

    /* Mobile notifications */
    .notification {
        margin: 8px;
        border-radius: 8px;
    }

    /* Mobile analytics modal */
    #analytics-modal .modal-content,
    #privacy-modal .modal-content,
    #checkout-modal .modal-content {
        margin: 0;
        height: 100vh;
        max-height: 100vh;
        border-radius: 0;
    }

    /* Mobile product modal */
    #product-modal .modal-content {
        margin: 10px;
        max-height: calc(100vh - 20px);
        border-radius: 16px;
    }

    /* Mobile cart summary */
    .cart-summary {
        position: sticky;
        bottom: 0;
        background: white;
        border-top: 1px solid #e5e7eb;
        padding: 16px;
        margin: 0 -16px;
    }

    .dark .cart-summary {
        background: #1f2937;
        border-top-color: #374151;
    }
}

/* Tablet Optimizations */
@media (min-width: 769px) and (max-width: 1024px) {
    .product-grid {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 20px;
    }

    #cart-sidebar,
    #favorites-sidebar {
        width: 420px;
    }

    .hero-title {
        font-size: 3.5rem;
    }
}

/* Touch device optimizations */
@media (hover: none) and (pointer: coarse) {
    /* Remove hover effects on touch devices */
    .hover\:bg-gray-100:hover {
        background-color: transparent;
    }

    .hover\:text-pink-600:hover {
        color: inherit;
    }

    /* Add active states instead */
    button:active,
    .btn:active {
        transform: scale(0.98);
        opacity: 0.8;
    }

    /* Better touch feedback */
    .ripple-effect {
        position: absolute;
        border-radius: 50%;
        background: rgba(255, 255, 255, 0.3);
        transform: scale(0);
        animation: ripple 0.6s linear;
        pointer-events: none;
    }

    @keyframes ripple {
        to {
            transform: scale(4);
            opacity: 0;
        }
    }
}

/* Landscape phone optimizations */
@media (max-width: 768px) and (orientation: landscape) {
    .hero-section {
        padding: 40px 0;
    }

    .hero-title {
        font-size: 2rem;
    }

    .modal-content {
        max-height: 90vh;
        overflow-y: auto;
    }
}

/* High DPI displays */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    /* Crisp images on retina displays */
    img {
        image-rendering: -webkit-optimize-contrast;
        image-rendering: crisp-edges;
    }
}

/* Accessibility improvements for mobile */
@media (max-width: 768px) {
    /* Larger focus indicators */
    button:focus,
    input:focus,
    textarea:focus {
        outline: 3px solid #ec4899;
        outline-offset: 2px;
    }

    /* Better contrast for small screens */
    .text-gray-500 {
        color: #6b7280;
    }

    .dark .text-gray-400 {
        color: #9ca3af;
    }
}

/* PWA optimizations */
@media (display-mode: standalone) {
    /* Hide browser UI elements when installed as PWA */
    .pwa-hidden {
        display: none;
    }

    /* Add safe area padding for notched devices */
    .safe-area-top {
        padding-top: env(safe-area-inset-top);
    }

    .safe-area-bottom {
        padding-bottom: env(safe-area-inset-bottom);
    }
}

/* Swipe gestures support */
.swipeable {
    touch-action: pan-x;
    user-select: none;
}

/* Mobile-first animations */
@media (max-width: 768px) {
    /* Reduce motion for better performance */
    .animate-bounce,
    .animate-pulse,
    .animate-spin {
        animation-duration: 0.5s;
    }

    /* Simpler transitions */
    .transition-all {
        transition: transform 0.2s ease, opacity 0.2s ease;
    }
}

.loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% {
        left: -100%;
    }
    100% {
        left: 100%;
    }
}

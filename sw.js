﻿// VelaSweets Service Worker
// Version 2.1.0 - Enhanced PWA Support

const CACHE_NAME = 'velasweets-v2.1.0';
const STATIC_CACHE = 'velasweets-static-v2.1.0';
const DYNAMIC_CACHE = 'velasweets-dynamic-v2.1.0';
const IMAGE_CACHE = 'velasweets-images-v2.1.0';

// Files to cache immediately
const STATIC_FILES = [
    '/',
    '/index.html',
    '/script.js',
    '/global.css',
    '/tailwind.min.css',
    '/js/security.js',
    '/js/products.js',
    '/js/cart.js',
    '/manifest.json'
];

// Images to cache
const CACHE_IMAGES = [
    '/images/جوزية.jpg',
    '/images/مادلين.jpg',
    '/images/حلى_ڤيلا.jpg'
];

// External resources to cache
const EXTERNAL_RESOURCES = [
    'https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap',
    'https://cdn.jsdelivr.net/npm/remixicon@3.5.0/fonts/remixicon.css'
];

// Install event - cache static files
self.addEventListener('install', event => {
    console.log('Service Worker: Installing...');
    
    event.waitUntil(
        Promise.all([
            // Cache static files
            caches.open(STATIC_CACHE).then(cache => {
                console.log('Service Worker: Caching static files');
                return cache.addAll(STATIC_FILES);
            }),
            
            // Cache images
            caches.open(IMAGE_CACHE).then(cache => {
                console.log('Service Worker: Caching images');
                return cache.addAll(CACHE_IMAGES);
            }),
            
            // Cache external resources
            caches.open(STATIC_CACHE).then(cache => {
                console.log('Service Worker: Caching external resources');
                return Promise.allSettled(
                    EXTERNAL_RESOURCES.map(url => 
                        cache.add(url).catch(err => console.warn('Failed to cache:', url, err))
                    )
                );
            })
        ]).then(() => {
            console.log('Service Worker: Installation complete');
            return self.skipWaiting();
        })
    );
});

// Activate event - clean up old caches
self.addEventListener('activate', event => {
    console.log('Service Worker: Activating...');
    
    event.waitUntil(
        caches.keys().then(cacheNames => {
            return Promise.all(
                cacheNames.map(cacheName => {
                    if (cacheName !== STATIC_CACHE && 
                        cacheName !== DYNAMIC_CACHE && 
                        cacheName !== IMAGE_CACHE) {
                        console.log('Service Worker: Deleting old cache:', cacheName);
                        return caches.delete(cacheName);
                    }
                })
            );
        }).then(() => {
            console.log('Service Worker: Activation complete');
            return self.clients.claim();
        })
    );
});

// Fetch event - serve cached content
self.addEventListener('fetch', event => {
    const { request } = event;
    const url = new URL(request.url);
    
    // Skip non-GET requests
    if (request.method !== 'GET') {
        return;
    }
    
    // Handle different types of requests
    if (request.destination === 'image') {
        event.respondWith(handleImageRequest(request));
    } else if (url.origin === location.origin) {
        event.respondWith(handleStaticRequest(request));
    } else {
        event.respondWith(handleExternalRequest(request));
    }
});

// Handle static file requests
async function handleStaticRequest(request) {
    try {
        // Try cache first
        const cachedResponse = await caches.match(request);
        if (cachedResponse) {
            return cachedResponse;
        }
        
        // If not in cache, fetch from network
        const networkResponse = await fetch(request);
        
        // Cache successful responses
        if (networkResponse.ok) {
            const cache = await caches.open(DYNAMIC_CACHE);
            cache.put(request, networkResponse.clone());
        }
        
        return networkResponse;
    } catch (error) {
        console.error('Service Worker: Static request failed:', error);
        
        // Return offline page for navigation requests
        if (request.mode === 'navigate') {
            return caches.match('/index.html');
        }
        
        // Return empty response for other requests
        return new Response('', { status: 408, statusText: 'Request Timeout' });
    }
}

// Handle image requests
async function handleImageRequest(request) {
    try {
        // Try cache first
        const cachedResponse = await caches.match(request);
        if (cachedResponse) {
            return cachedResponse;
        }
        
        // Fetch from network
        const networkResponse = await fetch(request);
        
        // Cache successful image responses
        if (networkResponse.ok) {
            const cache = await caches.open(IMAGE_CACHE);
            cache.put(request, networkResponse.clone());
        }
        
        return networkResponse;
    } catch (error) {
        console.error('Service Worker: Image request failed:', error);
        
        // Return placeholder image
        return new Response(
            '<svg xmlns="http://www.w3.org/2000/svg" width="400" height="300" viewBox="0 0 400 300"><rect width="100%" height="100%" fill="#f3f4f6"/><text x="50%" y="50%" text-anchor="middle" dy=".3em" fill="#9ca3af">صورة غير متوفرة</text></svg>',
            { headers: { 'Content-Type': 'image/svg+xml' } }
        );
    }
}

// Handle external requests
async function handleExternalRequest(request) {
    try {
        // Try cache first for external resources
        const cachedResponse = await caches.match(request);
        if (cachedResponse) {
            return cachedResponse;
        }
        
        // Fetch from network with timeout
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 5000);
        
        const networkResponse = await fetch(request, {
            signal: controller.signal
        });
        
        clearTimeout(timeoutId);
        
        // Cache successful responses
        if (networkResponse.ok) {
            const cache = await caches.open(STATIC_CACHE);
            cache.put(request, networkResponse.clone());
        }
        
        return networkResponse;
    } catch (error) {
        console.error('Service Worker: External request failed:', error);
        
        // Try to return cached version
        const cachedResponse = await caches.match(request);
        if (cachedResponse) {
            return cachedResponse;
        }
        
        // Return empty response
        return new Response('', { status: 408, statusText: 'Request Timeout' });
    }
}

// Background sync for offline actions
self.addEventListener('sync', event => {
    console.log('Service Worker: Background sync triggered:', event.tag);
    
    if (event.tag === 'contact-form') {
        event.waitUntil(syncContactForms());
    } else if (event.tag === 'order-submission') {
        event.waitUntil(syncOrders());
    }
});

// Sync contact forms when back online
async function syncContactForms() {
    try {
        const pendingForms = await getStoredData('pending_contact_forms');
        if (!pendingForms || pendingForms.length === 0) return;
        
        for (const form of pendingForms) {
            try {
                // Here you would send to your backend API
                console.log('Syncing contact form:', form);
                // await fetch('/api/contact', { method: 'POST', body: JSON.stringify(form) });
            } catch (error) {
                console.error('Failed to sync contact form:', error);
            }
        }
        
        // Clear pending forms after successful sync
        await clearStoredData('pending_contact_forms');
    } catch (error) {
        console.error('Background sync failed:', error);
    }
}

// Sync orders when back online
async function syncOrders() {
    try {
        const pendingOrders = await getStoredData('pending_orders');
        if (!pendingOrders || pendingOrders.length === 0) return;
        
        for (const order of pendingOrders) {
            try {
                // Here you would send to your backend API
                console.log('Syncing order:', order);
                // await fetch('/api/orders', { method: 'POST', body: JSON.stringify(order) });
            } catch (error) {
                console.error('Failed to sync order:', error);
            }
        }
        
        // Clear pending orders after successful sync
        await clearStoredData('pending_orders');
    } catch (error) {
        console.error('Order sync failed:', error);
    }
}

// Helper functions for IndexedDB operations
async function getStoredData(key) {
    return new Promise((resolve, reject) => {
        const request = indexedDB.open('VelaSweetsDB', 1);
        
        request.onerror = () => reject(request.error);
        request.onsuccess = () => {
            const db = request.result;
            const transaction = db.transaction(['sync_data'], 'readonly');
            const store = transaction.objectStore('sync_data');
            const getRequest = store.get(key);
            
            getRequest.onsuccess = () => resolve(getRequest.result?.data);
            getRequest.onerror = () => reject(getRequest.error);
        };
        
        request.onupgradeneeded = () => {
            const db = request.result;
            if (!db.objectStoreNames.contains('sync_data')) {
                db.createObjectStore('sync_data', { keyPath: 'key' });
            }
        };
    });
}

async function clearStoredData(key) {
    return new Promise((resolve, reject) => {
        const request = indexedDB.open('VelaSweetsDB', 1);
        
        request.onerror = () => reject(request.error);
        request.onsuccess = () => {
            const db = request.result;
            const transaction = db.transaction(['sync_data'], 'readwrite');
            const store = transaction.objectStore('sync_data');
            const deleteRequest = store.delete(key);
            
            deleteRequest.onsuccess = () => resolve();
            deleteRequest.onerror = () => reject(deleteRequest.error);
        };
    });
}

// Push notification handling
self.addEventListener('push', event => {
    console.log('Service Worker: Push notification received');
    
    const options = {
        body: event.data ? event.data.text() : 'إشعار جديد من VelaSweets',
        icon: '/images/icon-192x192.png',
        badge: '/images/badge-72x72.png',
        vibrate: [100, 50, 100],
        data: {
            dateOfArrival: Date.now(),
            primaryKey: 1
        },
        actions: [
            {
                action: 'explore',
                title: 'استكشاف المنتجات',
                icon: '/images/checkmark.png'
            },
            {
                action: 'close',
                title: 'إغلاق',
                icon: '/images/xmark.png'
            }
        ]
    };
    
    event.waitUntil(
        self.registration.showNotification('VelaSweets', options)
    );
});

// Notification click handling
self.addEventListener('notificationclick', event => {
    console.log('Service Worker: Notification clicked');
    
    event.notification.close();
    
    if (event.action === 'explore') {
        event.waitUntil(
            clients.openWindow('/')
        );
    }
});

// Message handling from main thread
self.addEventListener('message', event => {
    console.log('Service Worker: Message received:', event.data);
    
    if (event.data && event.data.type === 'SKIP_WAITING') {
        self.skipWaiting();
    }
});

console.log('Service Worker: Script loaded');
